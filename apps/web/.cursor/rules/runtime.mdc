---
description: 
globs: 
alwaysApply: true
---

 ## Dependencies

   "dependencies": {
    "@hookform/resolvers": "5.0.1",
    "@next/env": "15.3.1",
    "@reown/appkit": "1.7.6",
    "@reown/appkit-adapter-wagmi": "1.7.6",
    "@reown/appkit-siwe": "1.7.6",
    "@repo/ui": "workspace:*",
    "@tanstack/react-query": "5.71.10",
    "@tanstack/react-table": "^8.21.3",
    "@wagmi/core": "2.16.7",
    "@zodios/core": "^10.9.6",
    "drizzle-orm": "0.42.0",
    "lucide-react": "0.486.0",
    "motion": "^12.12.1",
    "next": "15.3.1",
    "next-themes": "0.4.6",
    "permissionless": "0.2.47",
    "react": "19.1.0",
    "react-dom": "19.1.0",
    "react-hook-form": "7.56.0",
    "tailwindcss": "4.1.0",
    "viem": "2.30.1",
    "wagmi": "2.15.4",
    "zod": "3.24.2",
    "zustand": "^5.0.5"
  },


  ## PackageManager

  pnpm