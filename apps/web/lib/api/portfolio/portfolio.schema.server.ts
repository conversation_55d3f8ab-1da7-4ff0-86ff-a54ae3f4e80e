import { z } from 'zod';

// Request Query Schemas
export const PortfolioActivitiesQuerySchema = z.object({
  page: z.number().int().gte(0).optional().default(0),
  limit: z.number().int().gt(0).lte(50).optional().default(50),
  filter: z.enum(['All', 'Predict', 'Redeem', 'Void']),
  order: z.enum(['newest', 'oldest']),
});

export const PortfolioDisputeQuerySchema = z.object({
  page: z.number().int().gte(0).optional().default(0),
  limit: z.number().int().gt(0).lte(50).optional().default(50),
  order: z.enum(['newest', 'value']).optional().default('newest'),
});

export const PortfolioPositionsQuerySchema = z.object({
  page: z.number().int().gte(0).optional().default(0),
  limit: z.number().int().gt(0).lte(50).optional().default(50),
  filter: z.enum(['all', 'live', 'ended']),
  order: z.enum(['volume', 'newest']),
});

// Response Schemas from OpenAPI
export const UserPortfolioResponseSchema = z.object({
  positionsValue: z.string(),
  profit: z.object({
    day: z.string(),
    week: z.string(),
    month: z.string(),
    total: z.string(),
  }),
});

export const ClaimableDisputeResponseSchema = z.object({
  claimable: z.string(),
});

// Additional Portfolio specific schemas
export const PortfolioProfitSchema = z.object({
  day: z.string(),
  week: z.string(),
  month: z.string(),
  total: z.string(),
});

export const PortfolioSummarySchema = z.object({
  positionsValue: z.string(),
  profit: PortfolioProfitSchema,
});

export const MarketStatusEnumSchema = z.enum([
  'OPEN',
  'REVIEWING',
  'DISPUTABLE',
  'DISPUTED',
  'CLOSED_WITHOUT_DISPUTE',
  'CLOSED_WITH_DISPUTE_ACCEPTED',
  'CLOSED_WITH_DISPUTE_REJECTED',
  'CANCELLED_WITH_UNMET',
  'CANCELLED_WITH_INVALID',
  'CANCELLED_WITH_OUTCOME_NOT_PROPOSED',
]);

export const PortfolioPostionItemSchema = z.object({
  market: z.object({
    id: z.string(),
    title: z.string(),
    imageUrl: z.string().nullable(),
    status: MarketStatusEnumSchema.optional(),
  }),
  user: z.object({
    address: z.string(),
    nickname: z.string(),
    imageUrl: z.string().nullable(),
  }),
  outcome: z.string(),
  outcomeOrder: z.number(),
  value: z.string(),
  estimatedOdds: z.string(),
  estimatedWin: z.string(),
  updatedAt: z.string(),
  redeemedAt: z.string().optional(),
  refundedAt: z.string().optional(),
});

export const PositionsResponseSchema = z.object({
  positions: z.array(PortfolioPostionItemSchema),
  totalLength: z.number().int().gte(0),
});

export const PortfolioDisputeSchema = z.object({
  id: z.string(),
  marketId: z.string(),
  marketTitle: z.string(),
  marketImageUrl: z.string().nullable(),
  disputeAmount: z.number().int(),
  status: z.enum(['PENDING', 'ACCEPTED', 'REJECTED']),
  reason: z.string(),
  createdAt: z.string(),
  resolvedAt: z.string().optional(),
});

export const PortfolioPositionsResponseSchema = z.object({
  positions: z.array(PortfolioPostionItemSchema),
  totalLength: z.number().int().gte(0),
  hasNext: z.boolean(),
});

export const PortfolioActivitySchema = z.object({
  market: z.object({
    id: z.string(),
    title: z.string(),
    imageUrl: z.string().nullable(),
  }),
  user: z.object({
    address: z.string(),
    nickname: z.string(),
    imageUrl: z.string().nullable(),
  }),
  outcomes: z.array(
    z.object({
      outcome: z.string(),
      outcomeOrder: z.number(),
    })
  ),
  amount: z.string(),
  transactionHash: z.string(),
  timestamp: z.string(),
  type: z.string(),
});

export const PortfolioActivitiesResponseSchema = z.object({
  activities: z.array(PortfolioActivitySchema),
  totalLength: z.number(),
});

export const PortfolioDisputesResponseSchema = z.object({
  disputes: z.array(PortfolioDisputeSchema),
  totalLength: z.number().int().gte(0),
});

// Type exports
export type UserPortfolioResponse = z.infer<typeof UserPortfolioResponseSchema>;
export type ClaimableDisputeResponse = z.infer<typeof ClaimableDisputeResponseSchema>;
export type PortfolioPositionsResponse = z.infer<typeof PortfolioPositionsResponseSchema>;
export type PortfolioDisputesResponse = z.infer<typeof PortfolioDisputesResponseSchema>;
export type PortfolioActivitiesResponse = z.infer<typeof PortfolioActivitiesResponseSchema>;

// Schema types for transforms
export type PortfolioPosition = z.infer<typeof PortfolioPostionItemSchema>;
export type PortfolioActivity = z.infer<typeof PortfolioActivitySchema>;
export type PortfolioDispute = z.infer<typeof PortfolioDisputeSchema>;

// Filter and order type unions
export type ActivityFilter = 'Predict' | 'Redeem' | 'Void';
export type ActivityOrder = 'newest' | 'oldest';
export type DisputeOrder = 'newest' | 'value';
export type PositionFilter = 'all' | 'live' | 'ended';
export type PositionOrder = 'volume' | 'newest';
