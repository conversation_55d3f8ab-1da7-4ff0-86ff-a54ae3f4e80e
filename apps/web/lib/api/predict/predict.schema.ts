import { z } from 'zod';
import { schemas } from '../openapi';
import { toAmount } from '@/lib/format';

export const ClaimRequestBodySchema = schemas.ClaimReqDto;

export const DepositChannelCollateralRequestBodySchema = z.object({
  amount: z.string(),
});

export const DepositDisputeCollateralRequestBodySchema = z.object({
  marketId: z.string().min(66).max(66),
  amount: z.string(),
  description: z.string().max(1000).optional(),
  referenceURL: z.string().optional(),
  fileURLs: z.array(z.string().url()).max(3).optional(),
});

export const PredictRequestBodySchema = z.object({
  marketId: z.string(),
  outcome: z.string(),
  amount: z.string().transform(val => toAmount(val).toString()),
});

export const RedeemRequestBodySchema = schemas.RedeemReqDto;

export const VoidRequestBodySchema = z.any();

export const WithdrawRequestBodySchema = schemas.WithdrawReqDto;

export const WithdrawChannelCollateralRequestBodySchema = z.object({
  amount: z.string(),
});

export const UserOperationRequestSchema = schemas.UserOperationDto;

// Response DTOs
// export const UserOperationSchema = schemas.UserOperationDto;

export const UserOperationSchema = z.object({
  paymaster: z.unknown().optional(),
  sender: z.unknown(),
  callData: z.unknown(),
  signature: z.unknown(),
  paymasterData: z.unknown().optional(),
  maxFeePerGas: z.coerce.bigint(),
  maxPriorityFeePerGas: z.coerce.bigint(),
  nonce: z.coerce.bigint(),
  paymasterPostOpGasLimit: z.coerce.bigint().optional(),
  callGasLimit: z.coerce.bigint(),
  preVerificationGas: z.coerce.bigint(),
  verificationGasLimit: z.coerce.bigint(),
  paymasterVerificationGasLimit: z.coerce.bigint().optional(),
  factory: z.unknown().optional(),
  factoryData: z.unknown().optional(),
});

export const GoResponseSchema = schemas.GoResDto;

// Type exports
export type ClaimRequestBody = z.infer<typeof ClaimRequestBodySchema>;
export type DepositChannelCollateralRequestBody = z.infer<
  typeof DepositChannelCollateralRequestBodySchema
>;
export type DepositDisputeCollateralRequestBody = z.infer<
  typeof DepositDisputeCollateralRequestBodySchema
>;
export type PredictRequestBody = z.infer<typeof PredictRequestBodySchema>;
export type RedeemRequestBody = z.infer<typeof RedeemRequestBodySchema>;
export type VoidRequestBody = z.infer<typeof VoidRequestBodySchema>;
export type WithdrawRequestBody = z.infer<typeof WithdrawRequestBodySchema>;
export type WithdrawChannelCollateralRequestBody = z.infer<
  typeof WithdrawChannelCollateralRequestBodySchema
>;
export type UserOperationRequest = z.infer<typeof UserOperationRequestSchema>;
export type UserOperation = z.infer<typeof UserOperationSchema>;
export type GoResponse = z.infer<typeof GoResponseSchema>;
