import {
  DEFAULT_CHANNEL_BANNER_URL,
  DEFAULT_MARKET_AVATAR_URL,
  MARKET_STATUS_TEXT_MAP,
} from '@/lib/constants';
import { formatUsdc } from '@/lib/format';
import { toLocalDate } from '@/lib/utils';
import { z } from 'zod';
import {
  ChannelCollateralHistoryResponseSchema,
  ChannelActivePredictionsResponseSchema,
  ChannelHistoryPredictionsResponseSchema,
  ChannelRewardsResponseSchema,
  ChannelRewardsHistoryResponseSchema,
  PopularChannelResponseSchema,
} from './channel.schema.server';
import type {
  ChannelInfoResponse,
  ChannelCollateralSchema,
  ChannelLeaderboardSchema,
  ActivePredictionSchema,
  HistoryPredictionSchema,
  RewardHistorySchema,
} from './channel.schema.server';

export const transformChannelInfo = (data: ChannelInfoResponse) => {
  return {
    ...data,
    imageUrl: data.imageUrl ?? DEFAULT_MARKET_AVATAR_URL,
    bannerUrl: data.bannerUrl ?? DEFAULT_CHANNEL_BANNER_URL,
  };
};

export const transformChannelCollateral = (data: ChannelCollateralSchema) => {
  const available = BigInt(data.available);
  const tied = BigInt(data.tied);
  const total = available + tied;

  return {
    available: formatUsdc(data.available),
    tied: formatUsdc(data.tied),
    total: formatUsdc(total),
    rawAvailable: available,
    rawTied: tied,
    rawTotal: total,
  };
};

export const transformChannelLeaderboard = (data: ChannelLeaderboardSchema) => {
  return {
    rankings: data.rankings.map(ranking => ({
      ...ranking,
      formattedPnl: formatUsdc(ranking.pnl),
      formattedVolume: formatUsdc(ranking.volume),
      rawPnl: BigInt(ranking.pnl),
      rawVolume: BigInt(ranking.volume),
    })),
    myRank: data.myRank
      ? {
          ...data.myRank,
          formattedPnl: formatUsdc(data.myRank.pnl),
          formattedVolume: formatUsdc(data.myRank.volume),
          rawPnl: BigInt(data.myRank.pnl),
          rawVolume: BigInt(data.myRank.volume),
        }
      : undefined,
  };
};

export const transformActivePrediction = (data: ActivePredictionSchema) => {
  return {
    ...data,
    imageUrl: data.imageUrl ?? DEFAULT_MARKET_AVATAR_URL,
    formattedTotalVolume: formatUsdc(data.totalVolume, { withDollarSign: true }),
    formattedDeposit: formatUsdc(data.deposit, { withDollarSign: true }),
    rawTotalVolume: BigInt(data.totalVolume),
    rawDeposit: BigInt(data.deposit),
    statusText: MARKET_STATUS_TEXT_MAP[data.status],
  };
};

export const transformActivePredictionsResponse = (
  data: z.infer<typeof ChannelActivePredictionsResponseSchema>
) => {
  return {
    ...data,
    markets: data.markets.map(transformActivePrediction),
  };
};

export const transformHistoryPrediction = (data: HistoryPredictionSchema) => {
  const date = new Date(data.finalizedAt);
  const formattedDate = date.toLocaleDateString('en-US', {
    month: 'short',
    day: 'numeric',
    year: 'numeric',
    hour: 'numeric',
    minute: '2-digit',
    hour12: true,
  });

  return {
    ...data,
    imageUrl: data.imageUrl ?? DEFAULT_MARKET_AVATAR_URL,
    finalizedAt: formattedDate,
    rewards: {
      prediction: formatUsdc(data.rewards.prediction),
      dispute: formatUsdc(data.rewards.dispute),
    },
    rawRewards: {
      prediction: BigInt(data.rewards.prediction),
      dispute: BigInt(data.rewards.dispute),
    },
  };
};

export const transformHistoryPredictionsResponse = (
  data: z.infer<typeof ChannelHistoryPredictionsResponseSchema>
) => {
  return {
    ...data,
    markets: data.markets.map(transformHistoryPrediction),
  };
};

export const transformChannelRewards = (data: z.infer<typeof ChannelRewardsResponseSchema>) => {
  return {
    formattedTotalRewards: formatUsdc(data.totalRewards),
    formattedClaimableRewards: formatUsdc(data.claimableRewards),
    rawTotalRewards: BigInt(data.totalRewards),
    rawClaimableRewards: BigInt(data.claimableRewards),
  };
};

export const transformRewardHistory = (data: RewardHistorySchema) => {
  return {
    ...data,
    formattedAmount: formatUsdc(data.amount),
    rawAmount: BigInt(data.amount),
    timestamp: toLocalDate(data.timestamp),
    claimedAt: data.claimedAt ? toLocalDate(data.claimedAt) : null,
    status: data.claimedAt ? 'Claimed' : 'Unclaimed',
  };
};

export const transformRewardsHistoryResponse = (
  data: z.infer<typeof ChannelRewardsHistoryResponseSchema>
) => {
  return {
    ...data,
    histories: data.histories.map(transformRewardHistory),
  };
};

export const transformCollateralHistoryResponse = (
  data: z.infer<typeof ChannelCollateralHistoryResponseSchema>
) => {
  return {
    ...data,
    histories: data.histories.map(history => ({
      ...history,
      formattedAmount: formatUsdc(history.amount),
      rawAmount: BigInt(history.amount),
      timestamp: toLocalDate(history.timestamp),
    })),
  };
};

export const transformPopularChannelsResponse = (
  data: z.infer<typeof PopularChannelResponseSchema>
) => {
  return {
    channels: data.channels.map(channel => ({
      ...channel,
      imageUrl: channel.imageUrl || undefined,
      formattedVolume: formatUsdc(channel.volume),
      rawVolume: BigInt(channel.volume),
    })),
  };
};
