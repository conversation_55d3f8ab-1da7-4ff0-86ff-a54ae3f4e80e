import { formatUsdc } from '@/lib/format';
import { toPercentage } from '@/lib/utils';
import type {
  ReferralBenefitResponse,
  ReferralBenefitsResponse,
  ReferralDashboardResponse,
  ReferralLeaderboardResponse,
  RewardDetailsResponse,
  ReferralBenefit,
  ReferralLeaderboardEntry,
  RewardDetail,
} from './referral.schema.server';

export const transformReferralBenefitResponse = (data: ReferralBenefitResponse) => {
  return {
    ...data,
    commissionRewardRatio: toPercentage(data.commissionRewardRatio),
    feeRebateRatio: toPercentage(data.feeRebateRatio),
    accumulatedProfit: formatUsdc(data.accumulatedProfit),
    profit: formatUsdc(data.profit),
  };
};

export const transformReferralBenefit = (data: ReferralBenefit) => {
  return {
    ...data,
    commissionRewardRatio: toPercentage(data.commissionRewardRatio),
    feeRebateRatio: toPercentage(data.feeRebateRatio),
    accumulatedProfit: formatUsdc(data.accumulatedProfit),
  };
};

export const transformReferralBenefitsResponse = (data: ReferralBenefitsResponse) => {
  return {
    ...data,
    benefits: data.benefits.map(transformReferralBenefit),
  };
};

export const transformReferralDashboardResponse = (data: ReferralDashboardResponse) => {
  return {
    totalCommissionReward: formatUsdc(data.totalCommissionReward),
    totalFeeRebate: formatUsdc(data.totalFeeRebate),
    rawTotalCommissionReward: BigInt(data.totalCommissionReward),
    rawTotalFeeRebate: BigInt(data.totalFeeRebate),
  };
};

export const transformReferralLeaderboardEntry = (data: ReferralLeaderboardEntry) => {
  return {
    ...data,
    commissionReward: formatUsdc(data.commissionReward),
    rawCommissionReward: BigInt(data.commissionReward),
  };
};

export const transformReferralLeaderboardResponse = (data: ReferralLeaderboardResponse) => {
  return {
    ...data,
    leaderboard: data.leaderboard.map(transformReferralLeaderboardEntry),
  };
};

export const transformRewardDetail = (data: RewardDetail) => {
  return {
    ...data,
    amount: formatUsdc(data.amount),
    rawAmount: BigInt(data.amount),
  };
};

export const transformRewardDetailsResponse = (data: RewardDetailsResponse) => {
  return {
    ...data,
    rewards: data.rewards.map(transformRewardDetail),
  };
};
