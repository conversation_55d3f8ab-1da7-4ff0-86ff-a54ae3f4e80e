import { ApiClient } from '@/lib/api/base-api';
import { ApiError } from '../base-api.error';
import {
  GetReferralDetailsRequest,
  ReferralBenefitResponseSchema,
  ReferralBenefitsResponseSchema,
  ReferralDashboardResponseSchema,
  ReferralLeaderboardResponseSchema,
  RewardDetailsResponseSchema,
} from './referral.schema.server';
import {
  transformReferralBenefitResponse,
  transformReferralBenefitsResponse,
  transformRewardDetailsResponse,
  transformReferralLeaderboardResponse,
  transformReferralDashboardResponse,
} from './referral.transform';

export class ReferralService {
  static BASE_PATH = '/data-api/v1/referral';
  static ROUTES = {
    GET: {
      '/benefit': 'benefit',
      '/benefits': 'benefits',
      '/details': 'details',
      '/leaderboard': 'leaderboard',
      '/total': 'total',
    },
  };

  private api: ApiClient;

  constructor() {
    this.api = new ApiClient(ReferralService.BASE_PATH);
  }

  /**
   * 특정 주소의 리퍼럴 혜택 조회
   */
  async getReferralBenefit() {
    const response = await this.api.get(ReferralService.ROUTES.GET['/benefit']);
    const result = ReferralBenefitResponseSchema.safeParse(response);

    if (!result.success) {
      throw ApiError.fromValidationError(
        result.error,
        ReferralService.ROUTES.GET['/benefit'],
        response
      );
    }
    return transformReferralBenefitResponse(result.data);
  }

  /**
   * 모든 리퍼럴 혜택 조회
   */
  async getReferralBenefits() {
    const response = await this.api.get(ReferralService.ROUTES.GET['/benefits']);
    const result = ReferralBenefitsResponseSchema.safeParse(response);

    if (!result.success) {
      throw ApiError.fromValidationError(
        result.error,
        ReferralService.ROUTES.GET['/benefits'],
        response
      );
    }
    return transformReferralBenefitsResponse(result.data);
  }

  /**
   * 리퍼럴 상세 내역 조회
   */
  async getReferralDetails(options: GetReferralDetailsRequest) {
    const searchParams = new URLSearchParams();
    if (options.page !== undefined) searchParams.set('page', options.page.toString());
    if (options.limit !== undefined) searchParams.set('limit', options.limit.toString());
    searchParams.set('type', options.type);

    const response = await this.api.get(ReferralService.ROUTES.GET['/details'], {
      searchParams,
    });

    const result = RewardDetailsResponseSchema.safeParse(response);
    if (!result.success) {
      throw ApiError.fromValidationError(
        result.error,
        ReferralService.ROUTES.GET['/details'],
        response
      );
    }
    return transformRewardDetailsResponse(result.data);
  }

  /**
   * 리퍼럴 리더보드 조회
   */
  async getReferralLeaderboard() {
    const response = await this.api.get(ReferralService.ROUTES.GET['/leaderboard']);
    const result = ReferralLeaderboardResponseSchema.safeParse(response);

    if (!result.success) {
      throw ApiError.fromValidationError(
        result.error,
        ReferralService.ROUTES.GET['/leaderboard'],
        response
      );
    }
    return transformReferralLeaderboardResponse(result.data);
  }

  /**
   * 리퍼럴 대시보드 총계 조회
   */
  async getReferralDashboard() {
    const response = await this.api.get(ReferralService.ROUTES.GET['/total']);
    const result = ReferralDashboardResponseSchema.safeParse(response);

    if (!result.success) {
      throw ApiError.fromValidationError(
        result.error,
        ReferralService.ROUTES.GET['/total'],
        response
      );
    }
    return transformReferralDashboardResponse(result.data);
  }
}

export const referralService = new ReferralService();
