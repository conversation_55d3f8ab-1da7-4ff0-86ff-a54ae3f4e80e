import { z } from 'zod';

export const BaseResponseSchema = <T extends z.ZodType>(resultSchema: T) =>
  z.object({
    code: z.string(),
    tid: z.string(),
    ts: z.number(),
    result: resultSchema,
  });

export const BaseResponseShapeSchema = z.object({
  code: z.string(),
  tid: z.string(),
  ts: z.number(),
  result: z.any(),
});

// ===================================================================
// Blockchain Event Schemas
// ===================================================================

export const EventRawSchema = z.object({
  id: z.number().int(),
  blockNumber: z.bigint(),
  blockHash: z.string(),
  blockTimestamp: z.date(),
  transactionHash: z.string(),
  transactionIndex: z.number().int(),
  logIndex: z.number().int(),
  contractAddress: z.string(),
  data: z.string(),
  topics: z.string(),
});

export const EventSchema = z.object({
  blockNumber: z.bigint(),
  logIndex: z.number().int(),
  eventName: z.string(),
  eventArgs: z.string(),
});

// ===================================================================
// Market Schemas
// ===================================================================

export const MarketSchema = z.object({
  id: z.string(),
  title: z.string(),
  description: z.string(),
  imageUrl: z.string().nullable(),
  channelId: z.string(),
  maker: z.string(),
  category: z.string(),
  collateralAmount: z.bigint(),
  totalVolume: z.bigint().default(BigInt(0)),
  competitive: z.number().default(0.5),
  referenceURL: z.string(),
  broadcastURL: z.string().nullable(),
  predictionDeadline: z.date(),
  resultConfirmDeadline: z.date(),
  disputedPeriod: z.string(),
  proposedOutcome: z.string().nullable(),
  finalOutcome: z.string().nullable(),
  outcomeProposedAt: z.date().nullable(),
  disputedAt: z.date().nullable(),
  finalizedAt: z.date().nullable(),
  createdAt: z.date().default(() => new Date()),
  pausedAt: z.date().nullable(),
});

export const PositionSchema = z.object({
  userAddress: z.string(),
  marketId: z.string(),
  outcome: z.string(),
  outcomeOrder: z.number().int(),
  value: z.bigint(),
  refundedAt: z.date().nullable(),
  redeemedAt: z.date().nullable(),
  createdAt: z.date().default(() => new Date()),
  updatedAt: z.date(),
});

export const MarketOutcomeSchema = z.object({
  marketId: z.string(),
  outcome: z.string(),
  order: z.number().int(),
  volume: z.string(),
});

export const MarketTagSchema = z.object({
  marketId: z.string(),
  tagName: z.string(),
});

// ===================================================================
// Category Schemas
// ===================================================================

export const CategorySchema = z.object({
  name: z.string(),
  imageUrl: z.string().nullable(),
  order: z.number().int(),
});

export const TagSchema = z.object({
  name: z.string(),
  category: z.string(),
  volume: z.bigint().default(BigInt(0)),
});

// ===================================================================
// User Schemas
// ===================================================================

export const UserSchema = z.object({
  address: z.string(),
  proxyAddress: z.string(),
  nickname: z.string(),
  bio: z.string().default(''),
  imageUrl: z.string().nullable(),
  isAdmin: z.boolean().default(false),
  referralCode: z.string(),
  referrer: z.string().nullable(),
  email: z.string().nullable(),
  createdAt: z.date().default(() => new Date()),
  updatedAt: z.date(),
});

export const ActivitySchema = z.object({
  id: z.number().int(),
  userAddress: z.string(),
  marketId: z.string().nullable(),
  type: z.string(),
  outcome: z.string().nullable(),
  outcomeOrder: z.number().int().nullable(),
  value: z.bigint().nullable(),
  distributionType: z.string().nullable(),
  category: z.string().nullable(),
  timestamp: z.date(),
  blockNumber: z.bigint(),
  logIndex: z.number().int(),
});

// ===================================================================
// Channel Schemas
// ===================================================================

export const ChannelSchema = z.object({
  id: z.string(),
  name: z.string(),
  description: z.string().nullable(),
  imageUrl: z.string().nullable(),
  commissionRate: z.number().default(0.04),
  createdAt: z.date().default(() => new Date()),
  updatedAt: z.date(),
});

export const ChannelManagerSchema = z.object({
  channelId: z.string(),
  managerAddress: z.string(),
  createdAt: z.date().default(() => new Date()),
});

export const ChannelCollateralSchema = z.object({
  channelId: z.string(),
  amount: z.bigint(),
  lockedAt: z.date().nullable(),
});

export const SubscriptionSchema = z.object({
  userAddress: z.string(),
  channelId: z.string(),
  createdAt: z.date().default(() => new Date()),
});

// ===================================================================
// Board Schemas
// ===================================================================

export const PostSchema = z.object({
  id: z.string(),
  refId: z.string(),
  author: z.string(),
  title: z.string(),
  content: z.string(),
  isPinned: z.boolean().default(false),
  createdAt: z.date().default(() => new Date()),
  updatedAt: z.date(),
});

export const CommentSchema = z.object({
  id: z.string(),
  parentId: z.string(),
  author: z.string(),
  content: z.string(),
  createdAt: z.date().default(() => new Date()),
  updatedAt: z.date(),
});

export const ReportedContentSchema = z.object({
  id: z.number().int(),
  contentId: z.string(),
  reporter: z.string(),
  createdAt: z.date().default(() => new Date()),
});

// ===================================================================
// Distribution Schemas
// ===================================================================

export const DistributionSchema = z.object({
  id: z.number().int(),
  userAddress: z.string(),
  marketId: z.string(),
  distributionType: z.string(),
  category: z.string(),
  value: z.bigint(),
  claimedAt: z.date().nullable(),
  sharedAt: z.date().nullable(),
  timestamp: z.date(),
  blockNumber: z.bigint(),
  logIndex: z.number().int(),
});

// ===================================================================
// Dispute Schemas
// ===================================================================

export const DisputeSchema = z.object({
  id: z.number().int(),
  marketId: z.string(),
  userAddress: z.string(),
  amount: z.bigint(),
  description: z.string().nullable(),
  referenceURL: z.string().nullable(),
  fileURLs: z.string().nullable(),
  claimedAt: z.date().nullable(),
  timestamp: z.date(),
  blockNumber: z.bigint(),
  logIndex: z.number().int(),
});

// ===================================================================
// Referral Schemas
// ===================================================================

export const ReferrerProfitSchema = z.object({
  userAddress: z.string(),
  month: z.string(),
  profit: z.bigint(),
  claimedAt: z.date().nullable(),
});

export const ReferralBenefitSchema = z.object({
  level: z.number().int(),
  commissionRewardRatio: z.number(),
  feeRebateRatio: z.number(),
  accumulatedProfit: z.bigint(),
});

// ===================================================================
// Mail Verification Schemas
// ===================================================================

export const MailVerificationSchema = z.object({
  userAddress: z.string(),
  email: z.string(),
  verificationId: z.string(),
});

// ===================================================================
// History Schemas
// ===================================================================

export const SeedHistorySchema = z.object({
  marketId: z.string(),
  userOpHash: z.string(),
});

// ===================================================================
// Type Exports
// ===================================================================

export type EventRaw = z.infer<typeof EventRawSchema>;
export type Event = z.infer<typeof EventSchema>;
export type Market = z.infer<typeof MarketSchema>;
export type Position = z.infer<typeof PositionSchema>;
export type MarketOutcome = z.infer<typeof MarketOutcomeSchema>;
export type MarketTag = z.infer<typeof MarketTagSchema>;
export type Category = z.infer<typeof CategorySchema>;
export type Tag = z.infer<typeof TagSchema>;
// export type User = z.infer<typeof UserSchema>;
export type Activity = z.infer<typeof ActivitySchema>;
export type Channel = z.infer<typeof ChannelSchema>;
export type ChannelManager = z.infer<typeof ChannelManagerSchema>;
export type ChannelCollateral = z.infer<typeof ChannelCollateralSchema>;
export type Subscription = z.infer<typeof SubscriptionSchema>;
export type Post = z.infer<typeof PostSchema>;
export type Comment = z.infer<typeof CommentSchema>;
export type ReportedContent = z.infer<typeof ReportedContentSchema>;
export type Distribution = z.infer<typeof DistributionSchema>;
export type Dispute = z.infer<typeof DisputeSchema>;
export type ReferrerProfit = z.infer<typeof ReferrerProfitSchema>;
export type ReferralBenefit = z.infer<typeof ReferralBenefitSchema>;
export type MailVerification = z.infer<typeof MailVerificationSchema>;
export type SeedHistory = z.infer<typeof SeedHistorySchema>;
