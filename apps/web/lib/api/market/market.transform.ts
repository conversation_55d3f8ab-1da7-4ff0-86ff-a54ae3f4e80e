import { DEFAULT_MARKET_AVATAR_URL, MARKET_STATUS_TEXT_MAP } from '@/lib/constants';
import type {
  GetMarketsResponseType,
  MarketActivitiesResponseSchema,
  MarketActivitySchema,
  MarketServerSchema,
  MarketTopPredictorsResponseSchema,
} from './market.schema.server';
import { formatUsdc } from '@/lib/format';
import { toRelativeTime } from '@/lib/utils';

export const transformMarket = (data: MarketServerSchema) => {
  return {
    ...data,
    imageUrl: data.imageUrl ?? DEFAULT_MARKET_AVATAR_URL,
    channel: {
      ...data.channel,
      imageUrl: data.channel.imageUrl ?? DEFAULT_MARKET_AVATAR_URL,
    },
    outcomes: data.outcomes.map(outcome => ({
      ...outcome,
      formattedVolume: formatUsdc(outcome.volume),
      rawVolume: BigInt(outcome.volume),
    })),
    formattedTotalVolume: formatUsdc(data.totalVolume),
    rawTotalVolume: BigInt(data.totalVolume),
    formattedCollateralAmount: formatUsdc(data.collateralAmount),
    rawCollateralAmount: BigInt(data.collateralAmount),
    formattedMaxOutcomeVolume: formatUsdc(data.maxOutcomeVolume),
    rawMaxOutcomeVolume: BigInt(data.maxOutcomeVolume),
    statusText: MARKET_STATUS_TEXT_MAP[data.status],
  };
};

export const transformGetMarketsResponse = (data: GetMarketsResponseType) => {
  return {
    ...data,
    markets: data.markets.map(transformMarket),
  };
};

export const transformMarketTopPredictorsResponse = (data: MarketTopPredictorsResponseSchema) => {
  return {
    ...data,
    topPredictors: data.topPredictors.map(predictor => ({
      ...predictor,
      imageUrl: predictor.imageUrl ?? DEFAULT_MARKET_AVATAR_URL,
      formattedAmount: formatUsdc(predictor.amount),
      rawAmount: BigInt(predictor.amount),
    })),
  };
};

const transformMarketActivity = (data: MarketActivitySchema) => {
  return {
    ...data,
    market: {
      ...data.market,
      imageUrl: data.market.imageUrl ?? DEFAULT_MARKET_AVATAR_URL,
    },
    user: {
      ...data.user,
      imageUrl: data.user.imageUrl ?? DEFAULT_MARKET_AVATAR_URL,
    },
    formattedAmount: formatUsdc(data.amount),
    rawAmount: BigInt(data.amount),
    relativeTime: toRelativeTime(data.timestamp) + ' ago',
  };
};

export const transformMarketActivitiesResponse = (data: MarketActivitiesResponseSchema) => {
  return {
    ...data,
    activities: data.activities.map(transformMarketActivity),
  };
};
