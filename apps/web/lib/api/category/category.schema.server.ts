import { z } from 'zod';
import { schemas } from '../openapi';

export const CategoryPageResponseSchema = schemas.CategoryPageResDto;
export const CategoriesResponseSchema = z.object({
  categories: z.array(
    z.object({
      name: z.string().min(1).max(50),
    })
  ),
  totalLength: z.number(),
});

export type CategoryPageResponse = z.infer<typeof CategoryPageResponseSchema>;
export type CategoriesResponse = z.infer<typeof CategoriesResponseSchema>;
export type MarketOrder = 'VOLUME' | 'NEWEST' | 'ENDING_SOON' | 'COMPETITIVE';
