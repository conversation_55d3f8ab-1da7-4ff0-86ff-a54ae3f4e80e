import { z } from 'zod';
import { schemas } from '../openapi';
import { DEFAULT_USER_AVATAR_URL } from '@/lib/constants';

// Request DTOs
export const CreateCommentRequestSchema = z.object({ content: z.string() });
export const CreateChannelPostRequestSchema = schemas.CreateChannelPostReqDto;
export const EditChannelPostRequestSchema = schemas.EditChannelPostReqDto;
export const ReportPostRequestSchema = z.object({ postId: z.string() });

export const CommentsResponseSchema = z.object({
  comments: z.array(
    z.object({
      id: z.string(),
      parentId: z.string(),
      content: z.string(),
      createdAt: z.string(),
      author: z.object({
        address: z.string(),
        nickname: z.string(),
        imageUrl: z.string().nullable(),
      }),
      likes: z.number(),
      commentCount: z.number(),
      isLiked: z.boolean(),
      positions: z
        .array(
          z.object({
            outcome: z.string(),
            outcomeOrder: z.number(),
            value: z.string(),
          })
        )
        .optional(),
    })
  ),
  totalLength: z.number(),
});

export const CommentResponseSchema = schemas.CommentResDto;
export const LikeResponseSchema = schemas.LikeResDto;
export const PostResponseSchema = schemas.PostResDto;
export const PostsResponseSchema = schemas.PostsResDto;
export const SuccessResponseSchema = z.object({ success: z.boolean() });

// Type exports
export type CreateCommentRequest = z.infer<typeof CreateCommentRequestSchema>;
export type CreateChannelPostRequest = z.infer<typeof CreateChannelPostRequestSchema>;
export type EditChannelPostRequest = z.infer<typeof EditChannelPostRequestSchema>;
export type ReportPostRequest = z.infer<typeof ReportPostRequestSchema>;

export type CommentsResponse = z.infer<typeof CommentsResponseSchema>;
export type CommentResponse = z.infer<typeof CommentResponseSchema>;
export type LikeResponse = z.infer<typeof LikeResponseSchema>;
export type PostResponse = z.infer<typeof PostResponseSchema>;
export type PostsResponse = z.infer<typeof PostsResponseSchema>;
export type SuccessResponse = z.infer<typeof SuccessResponseSchema>;
export type CommentSchema = z.infer<typeof CommentsResponseSchema>['comments'][number];

// Enums and types
export type CommentType = 'board' | 'market';
export type CommentOrder = 'latest' | 'likes';
export type PredictorsFilter = 'true' | 'false';
