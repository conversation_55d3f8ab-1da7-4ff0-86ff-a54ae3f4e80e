import type { SIWEVerifyMessageArgs, SIWECreateMessageArgs, SIWESession } from '@reown/appkit-siwe';
import { createSIWEConfig, formatMessage } from '@reown/appkit-siwe';
import { AuthApis } from '@/lib/api/auth/auth.service';
import { getSmartAccount, networkIds } from './wagmi';
import { globalStore } from '@/store/global.store';

const authApis = new AuthApis();

export const siweConfig = createSIWEConfig({
  sessionRefetchIntervalMs: 1000 * 60 * 5,
  nonceRefetchIntervalMs: 1000 * 60 * 5,
  signOutOnDisconnect: true,
  signOutOnAccountChange: true,
  signOutOnNetworkChange: true,
  required: true,
  enabled: true,
  onSignIn: session => handleSession(session),
  onSignOut: () => {
    globalStore.clearSession();
  },
  getMessageParams: async () => ({
    domain: typeof window !== 'undefined' ? window.location.host : '',
    uri: typeof window !== 'undefined' ? window.location.origin : '',
    chains: networkIds,
    statement: 'Please sign with your account',
  }),
  createMessage: ({ address, ...args }: SIWECreateMessageArgs) => formatMessage(args, address),
  getNonce: async (address?: string) => {
    const nonceResponse = await authApis.getNonce(address || crypto.randomUUID()); // walletConnect QR은 address가 셋업되기 전이라 난수 생성
    if (!nonceResponse || !nonceResponse.nonce) {
      throw new Error('Failed to get nonce!');
    }

    return nonceResponse.nonce;
  },
  getSession: async () => {
    const session = await authApis.getSession();
    if (!session.siwe) {
      return null;
    }

    // Validate address and chainId types
    if (typeof session.siwe.address !== 'string' || typeof session.siwe.chainId !== 'number') {
      return null;
    }

    const siweSession = {
      address: session.siwe.address,
      chainId: session.siwe.chainId,
    } satisfies SIWESession;

    handleSession(siweSession);

    return siweSession;
  },
  verifyMessage: async ({ message, signature }: SIWEVerifyMessageArgs) => {
    try {
      const { success } = await authApis.verify({
        message,
        signature,
      });

      const isVerified = Boolean(success);

      return isVerified;
    } catch (error: unknown) {
      console.error('SIWE verification failed:', error);
      return false;
    }
  },
  signOut: async () => {
    try {
      await authApis.signOut();
      return true;
    } catch (error: unknown) {
      return false;
    }
  },
});

/**
 * 세션 처리 함수
 * @param session SIWE 세션 정보
 */
const handleSession = (session: SIWESession | undefined) => {
  if (!session) {
    return globalStore.clearSession();
  }
  globalStore.setSession(session);
  // getSmartAccount(session.address)
  //   .then(smartAccount => {
  //     globalStore.setSafeSmartAccount(smartAccount);
  //   })
  //   .catch(error => {
  //     console.error(
  //       '🤔🤔🤔🤔🤔🤔🤔🤔🤔🤔🤔🤔🤔🤔🤔🤔🤔🤔🤔🤔🤔🤔🤔🤔 - Failed to get smart account:',
  //       error
  //     );
  //   });
};
