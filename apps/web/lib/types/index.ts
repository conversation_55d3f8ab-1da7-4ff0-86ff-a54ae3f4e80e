import type { ToSafeSmartAccountReturnType } from 'permissionless/accounts';
import type { Address } from 'viem';

export type MarketStatus = 'Live' | 'Pending' | 'Disputable' | 'Win a dispute' | 'Under Review';

export type MarketOutcome = {
  marketId: string;
  outcome: string;
  order: number;
  volume: string;
};

export type Market = {
  id: string;
  title: string;
  status: MarketStatus;
  endDate: Date | string | number;
  imageUrl: string;
  volume: string;
  participants: number;
  outcomes: MarketOutcome[];
  maxOutcomeVolume: string;
  marketTotalVolume: string;
};

export type Channel = {
  id: string;
  name: string;
  imageUrl: string;
};

export type User = {
  address: string;
  nickname: string;
  imageUrl: string | null;
  bio: string;
  smartAccountAddress: Address | null;
  channelId: Address;
};

export type SafeSmartAccount = ToSafeSmartAccountReturnType<'0.7'>;
