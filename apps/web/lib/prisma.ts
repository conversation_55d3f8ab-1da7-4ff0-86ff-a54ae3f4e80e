import { PrismaClient } from '@prisma/client';
import { drizzle } from 'drizzle-orm/prisma/mysql';
import * as schema from '@/db/drizzle/schema'; // Import generated Drizzle schema

declare global {
  // allow global `var` declarations
  // eslint-disable-next-line no-var
  var prisma: PrismaClient | undefined;
}

const prismaInstance = global.prisma ?? new PrismaClient();

if (process.env.NODE_ENV !== 'production') global.prisma = prismaInstance;

// Extend the Prisma client instance with Drizzle
// The schema is implicitly known through the generator and used via prisma.$drizzle
export const prisma = prismaInstance.$extends(drizzle());

// Export the original Prisma client if needed separately
// export const prismaRaw = prismaInstance;
