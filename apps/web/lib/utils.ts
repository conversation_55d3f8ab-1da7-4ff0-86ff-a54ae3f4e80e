import { concat, pad, toHex, type Hex } from 'viem';
import type { UserOperation } from 'viem/account-abstraction';
import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';

dayjs.extend(relativeTime);

export function copyToClipboard(text: string) {
  navigator.clipboard.writeText(text);
}

export function isBrowser() {
  if (typeof window === 'undefined') {
    return false;
  }
  return true;
}

export function createPaymasterAndData(userOp: UserOperation): Hex {
  return concat([
    userOp.paymaster as Hex,
    pad(toHex(userOp.paymasterVerificationGasLimit || BigInt(0)), {
      size: 16,
    }),
    pad(toHex(userOp.paymasterPostOpGasLimit || BigInt(0)), {
      size: 16,
    }),
    (userOp.paymasterData as Hex) || ('0x' as Hex),
  ]);
}

export function convertUserOpToBigInt(userOp: any) {
  return {
    ...userOp,
    maxFeePerGas: BigInt(userOp.maxFeePerGas),
    maxPriorityFeePerGas: BigInt(userOp.maxPriorityFeePerGas),
    nonce: BigInt(userOp.nonce),
    paymasterPostOpGasLimit: BigInt(userOp.paymasterPostOpGasLimit),
    callGasLimit: BigInt(userOp.callGasLimit),
    preVerificationGas: BigInt(userOp.preVerificationGas),
    verificationGasLimit: BigInt(userOp.verificationGasLimit),
    paymasterVerificationGasLimit: BigInt(userOp.paymasterVerificationGasLimit),
  };
}
export function toLocalDate(date: string | Date | number) {
  return new Date(date).toLocaleDateString('en-US', {
    month: 'short',
    day: 'numeric',
    year: 'numeric',
    hour: 'numeric',
    minute: '2-digit',
    hour12: true,
  });
}

export function toRelativeTime(date: string | Date | number) {
  return dayjs(date).fromNow(true) + ' ago';
}

export function toPercentage(value: number | string) {
  if (typeof value === 'string') {
    value = Number(value);
  }

  return (value * 100).toFixed(2) + '%';
}

export function toTxUrl(txHash: string) {
  return `https://amoy.polygonscan.com/tx/${txHash}`;
}
