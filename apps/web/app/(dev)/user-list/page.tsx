import { MainLayout } from '@/components/layouts/main-layout'; // Assuming MainLayout is used for dev pages too
import { prisma } from '@/lib/prisma';
import { User } from '@/db/drizzle/schema';
import type { InferSelectModel } from 'drizzle-orm';

// type UserSelectType = InferSelectModel<typeof User>;

export default function DevUserListPage() {
  // let allUsers: UserSelectType[] = [];
  // try {
  //   allUsers = await prisma.$drizzle.select().from(User);
  // } catch (error) {
  //   console.error('Failed to fetch users:', error);
  // }

  return (
    <MainLayout>
      <div className="container mx-auto py-8">
        <h1 className="mb-4 text-2xl font-bold">Dev - All Users</h1>
        <div className="w-full rounded-md border p-4">
          {/* <h2 className="mb-2 text-lg font-semibold">All Users ({allUsers.length})</h2>
          {allUsers.length > 0 ? (
            <ul className="space-y-4">
              {allUsers.map(user => (
                <li key={user.address} className="border-b pb-2">
                  <pre className="text-sm whitespace-pre-wrap">{JSON.stringify(user, null, 2)}</pre>
                </li>
              ))}
            </ul>
          ) : (
            <p>No users found in the database.</p>
          )} */}
        </div>
      </div>
    </MainLayout>
  );
}
