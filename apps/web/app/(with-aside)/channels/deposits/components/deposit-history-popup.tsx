'use client';

import React, { useState } from 'react';
import { cn } from '@repo/ui/lib/utils';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@repo/ui/components/table';
import { useCollateralHistory } from '@/hooks/query/channel';
import { BaseButton } from '@/components/ui/base.button';

// Type mapping for collateral history types
const getHistoryTypeLabel = (type: string, category?: string) => {
  switch (type) {
    case 'deposit':
      return 'Add Deposit';
    case 'withdraw':
      return 'Withdraw';
    case 'dispute_win':
      return 'Win a dispute';
    case 'dispute_lose':
      return 'Lose a dispute';
    default:
      return type;
  }
};

// Format date to local time string
const formatDateTime = (dateString: string): string => {
  const date = new Date(dateString);
  return date.toLocaleString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
    hour12: true,
  });
};

// Format amount to 2 decimal places
const formatAmount = (amount: number): string => {
  return (amount / 100).toLocaleString('en-US', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  });
};

interface DepositHistoryPopupProps {
  className?: string;
  onClose?: () => void;
}

export default function DepositHistoryPopup({ className, onClose }: DepositHistoryPopupProps) {
  const [page, setPage] = useState(0);
  const limit = 20;

  const {
    data: historyData,
    isLoading,
    error,
  } = useCollateralHistory({
    page,
    limit,
  });

  const histories = historyData?.histories || [];
  const totalLength = historyData?.totalLength || 0;
  const totalPages = Math.ceil(totalLength / limit);
  const hasNext = page < totalPages - 1;
  const hasPrevious = page > 0;

  const handlePreviousPage = () => {
    if (hasPrevious) {
      setPage(page - 1);
    }
  };

  const handleNextPage = () => {
    if (hasNext) {
      setPage(page + 1);
    }
  };

  return (
    <div className={cn('flex w-full max-w-(--popup-width) flex-col', className)}>
      {/* Header */}
      <div className="mb-space-20 text-center">
        <h2 className="text-size-lg text-mid-dark font-semibold">Deposit History</h2>
      </div>

      {/* Content */}
      <div>
        {error ? (
          <div className="text-no-red text-size-sm py-10 text-center">
            Failed to load deposit history. Please try again.
          </div>
        ) : (
          <>
            {/* Table Header */}
            <Table>
              <TableHeader className="border-b-line border-b">
                <TableRow className="">
                  <TableHead className="text-size-sm text-gray-3 w-1/3 font-semibold">
                    Results
                  </TableHead>
                  <TableHead className="text-size-sm text-gray-3 w-1/3 text-right font-semibold">
                    Amount
                  </TableHead>
                  <TableHead className="text-size-sm text-gray-3 w-1/3 text-right font-semibold">
                    Time
                  </TableHead>
                </TableRow>
              </TableHeader>
            </Table>

            {/* Table Body with scroll */}
            <div className="max-h-[300px] overflow-y-auto">
              <Table>
                <TableBody>
                  {isLoading ? (
                    <TableRow className="border-0">
                      <TableCell colSpan={3} className="py-10 text-center">
                        <div className="text-gray-3 text-size-sm">Loading...</div>
                      </TableCell>
                    </TableRow>
                  ) : histories.length === 0 ? (
                    <TableRow className="border-0">
                      <TableCell colSpan={3} className="py-10 text-center">
                        <div className="text-gray-3 text-size-sm">No Deposit History</div>
                      </TableCell>
                    </TableRow>
                  ) : (
                    histories.map((history, index) => (
                      <TableRow
                        key={`${history.timestamp}-${index}`}
                        className="border-b-line border-b last:border-b-0"
                      >
                        <TableCell className="text-size-xs text-mid-dark py-space-15">
                          {getHistoryTypeLabel(history.type, history.category)}
                        </TableCell>
                        <TableCell className="text-size-xs text-mid-dark py-space-15 text-right font-semibold">
                          ${formatAmount(Number(history.amount))}
                        </TableCell>
                        <TableCell className="text-size-xs text-mid-dark py-space-15 text-right">
                          {formatDateTime(history.timestamp)}
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>

            {/* Pagination */}
            {histories.length > 0 && totalPages > 1 && (
              <div className="bg-gray-2 border-t-line py-space-10 gap-space-10 flex items-center justify-center border-t">
                <BaseButton
                  variant="ghost"
                  size="icon"
                  className="size-space-36 p-0"
                  onClick={handlePreviousPage}
                  disabled={!hasPrevious || isLoading}
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="20"
                    height="20"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="lucide lucide-chevron-left"
                  >
                    <path d="m15 18-6-6 6-6" />
                  </svg>
                </BaseButton>
                <div className="gap-space-10 flex items-center">
                  {[...Array(totalPages)].map((_, i) => (
                    <BaseButton
                      key={i}
                      variant={i === page ? 'neutral' : 'ghost'}
                      size="icon"
                      className={cn(
                        'size-space-36 p-0',
                        i === page ? 'text-mid-dark' : 'text-gray-3'
                      )}
                      onClick={() => setPage(i)}
                      disabled={isLoading}
                    >
                      {i + 1}
                    </BaseButton>
                  ))}
                </div>
                <BaseButton
                  variant="ghost"
                  size="icon"
                  className="size-space-36 p-0"
                  onClick={handleNextPage}
                  disabled={!hasNext || isLoading}
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="20"
                    height="20"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="lucide lucide-chevron-right"
                  >
                    <path d="m9 18 6-6-6-6" />
                  </svg>
                </BaseButton>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
}
