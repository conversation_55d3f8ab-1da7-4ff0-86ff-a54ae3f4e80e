import SvgIcon from '@/components/icons/svg-icon';
import PageContainer from '@/components/layouts/page-container';
import { pxToRem } from '@repo/ui/lib/utils';
import DepositsDashboard from './components/deposits-dashboard';

export default function DepositsPage() {
  return (
    <PageContainer title="Deposit System">
      <div>
        <section className="gap-space-60 flex flex-col pb-[80px]">
          <div className="flex">
            <p className="text-size-sm text-gray-3">
              The deposit system is a mechanism designed to ensure the proper operation of
              predictions and the confirmation of results by channel leaders.
            </p>
          </div>
          <div
            style={
              {
                '--card-width': pxToRem(400),
                '--card-height': pxToRem(95),
                '--spacing': pxToRem(60),
              } as React.CSSProperties
            }
            className="flex flex-col gap-[var(--spacing)] [&_div[data-role='card']]:h-[var(--card-height)] [&_div[data-role='card']]:w-[var(--card-width)]"
          >
            <div
              data-role="card"
              className="border-line bg-gray-2 relative flex flex-col items-center justify-center gap-[5px] rounded-full border"
            >
              <SvgIcon data-label="icon" name="MarketIcon" className="text-yes-green size-[24px]" />
              <p className="text-size-sm text-mid-dark font-semibold">Create Prediction</p>
              <div className="absolute top-[calc(100%+15px)] left-[50%] translate-x-[-50%]">
                <ArrowDown />
              </div>
            </div>

            <div className="gap-space-30 flex items-center">
              <div
                data-role="card"
                className="border-line bg-gray-2 relative flex flex-none flex-col items-center justify-center gap-[5px] rounded-full border"
              >
                <SvgIcon
                  data-label="icon"
                  name="MarketIcon"
                  className="text-yes-green size-[24px]"
                />
                <p className="text-size-sm text-mid-dark font-semibold">Set Deposit Amount</p>
                <div className="absolute top-[calc(100%+15px)] left-[50%] translate-x-[-50%]">
                  <ArrowDown />
                </div>
              </div>
              <p className="text-size-sm text-gray-3">
                The deposit system is a mechanism designed to ensure the proper operation of
                predictions and the confirmation of results by channel leaders.
              </p>
            </div>

            <div
              data-role="card"
              className="border-line bg-gray-2 relative flex flex-col items-center justify-center gap-[5px] rounded-full border"
            >
              <SvgIcon data-label="icon" name="MarketIcon" className="text-yes-green size-[24px]" />
              <p className="text-size-sm text-mid-dark font-semibold">
                Continue outcome after prediction ends
              </p>
              <div className="absolute top-[calc(100%+15px)] left-[50%] translate-x-[-50%]">
                <ArrowDown />
              </div>
            </div>
            <div className="gap-space-30 flex items-center">
              <div
                data-role="card"
                className="border-line bg-gray-2 relative flex flex-none flex-col items-center justify-center gap-[5px] rounded-full border"
              >
                <SvgIcon
                  data-label="icon"
                  name="MarketIcon"
                  className="text-yes-green size-[24px]"
                />
                <p className="text-size-sm text-mid-dark font-semibold">Raise a dispute</p>
                <div className="absolute top-[calc(100%+15px)] left-[50%] translate-x-[-50%]">
                  <ArrowDown />
                </div>
              </div>
              <p className="text-size-sm text-gray-3">
                All users may dispute the finalized result by placing the same deposit amount on
                their chosen option.
              </p>
            </div>

            <div className="gap-space-30 flex items-center">
              <div
                data-role="card"
                className="border-line bg-gray-2 flex flex-none flex-col items-center justify-center gap-[5px] rounded-full border"
              >
                <SvgIcon
                  data-label="icon"
                  name="MarketIcon"
                  className="text-yes-green size-[24px]"
                />
                <p className="text-size-sm text-mid-dark font-semibold">
                  PredictGo reviews and finalize the result
                </p>
              </div>
              <p className="text-size-sm text-gray-3">
                PredictGo will comprehensively review disputes and make a final decision regarding
                the result. <br />
                <span className="text-no-red">
                  If a prediction outcome is finalized incorrectly, the deposit amount will be
                  forfeited.
                </span>
              </p>
            </div>
          </div>
        </section>
        <DepositsDashboard />
      </div>
    </PageContainer>
  );
}

const ArrowDown = () => {
  return (
    <svg width="10" height="30" viewBox="0 0 10 30" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M5 30L9.33013 22.5L0.669872 22.5L5 30ZM4.25 -3.27835e-08L4.25 23.25L5.75 23.25L5.75 3.27835e-08L4.25 -3.27835e-08Z"
        fill="#3B424B"
      />
    </svg>
  );
};
