'use client';

import CommonAvatar from '@/components/ui/avatar-image';
import { InfoButton } from '@/components/ui/base.button';
import { useActivePredictions } from '@/hooks/query/channel/use-active-predictions';
import { ActivePrediction } from '@/lib/api/channel/channel.schema';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@repo/ui/components/table';
import { ColumnDef, flexRender, getCoreRowModel, useReactTable } from '@tanstack/react-table';
import * as React from 'react';
import AddMarketDepositButton from '@/components/actions/add-market-deposit-button';
import ProposeAnswerButton from '@/components/actions/propose-answer-button';
import Link from 'next/link';

const columns: ColumnDef<ActivePrediction>[] = [
  {
    accessorKey: 'title',
    header: 'Prediction',
    cell: ({ row }) => {
      const prediction = row.original;
      return (
        <Link href={`/markets/${prediction.id}`}>
          <div className="gap-space-20 flex items-center">
            <CommonAvatar size="md2" imageUrl={prediction.imageUrl} />
            <div className="flex flex-col">
              <div className="text-mid-dark text-size-sm font-semibold">{prediction.title}</div>
              <div className="gap-space-10 flex items-center">
                <div className="text-size-xs gap-space-6 flex items-center">
                  {/* TODOL add createdAt */}
                  {/* <span className="text-mid-dark font-semibold">{prediction.totalVolume}</span> */}
                  {/* <span>Vol.</span> */}
                </div>
              </div>
            </div>
          </div>
        </Link>
      );
    },
  },
  {
    accessorKey: 'totalVolume',
    header: 'Total Volume',
    cell: ({ row }) => (
      <div className="text-mid-dark text-size-sm font-semibold">{row.original.totalVolume}</div>
    ),
  },
  {
    accessorKey: 'deposit',
    header: 'Deposit',
    cell: ({ row }) => (
      <div className="text-mid-dark text-size-sm font-semibold">{row.original.deposit}</div>
    ),
  },
  {
    accessorKey: 'status',
    header: 'Status',
    cell: ({ row }) => {
      return (
        <div className="text-mid-dark text-size-sm font-medium">{row.original.statusText}</div>
      );
    },
  },
  {
    id: 'actions',
    cell: ({ row }) => {
      const status = row.original.status;
      if (status === 'OPEN') {
        return <AddMarketDepositButton marketId={row.original.id} />;
      }

      if (status === 'REVIEWING') {
        return <ProposeAnswerButton marketId={row.original.id} className="w-[140px]" />;
      }

      return <div className="w-[140px]"></div>;
    },
  },
];

export default function ActivePredictionsTable() {
  const [rowSelection, setRowSelection] = React.useState({});
  const { data, isLoading, error } = useActivePredictions({ page: 0, limit: 50 });

  const table = useReactTable<ActivePrediction>({
    data: data?.markets || [],
    columns: columns as ColumnDef<ActivePrediction>[],
    getCoreRowModel: getCoreRowModel(),
    onRowSelectionChange: setRowSelection,
    state: {
      rowSelection,
    },
  });

  if (isLoading) {
    return (
      <div className="w-full">
        <div className="p-4">
          <div className="flex h-24 items-center justify-center">Loading...</div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="w-full">
        <div className="p-4">
          <div className="flex h-24 items-center justify-center text-red-500">
            Error loading predictions
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full">
      <div className="p-4">
        <Table>
          <TableHeader className="">
            {table.getHeaderGroups().map(headerGroup => (
              <TableRow key={headerGroup.id} className="border-b-line border-b-0">
                {headerGroup.headers.map(header => (
                  <TableHead className="text-size-sm text-gray-3 font-semibold" key={header.id}>
                    {header.isPlaceholder
                      ? null
                      : flexRender(header.column.columnDef.header, header.getContext())}
                  </TableHead>
                ))}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map(row => (
                <TableRow key={row.id} className="border-0">
                  {row.getVisibleCells().map(cell => (
                    <TableCell key={cell.id}>
                      {flexRender(cell.column.columnDef.cell, cell.getContext())}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow className="border-0">
                <TableCell colSpan={columns.length} className="pt-space-50 h-full text-center">
                  <div className="gap-space-10 flex flex-col items-center">
                    <img src="/no-markets-available.svg" alt="No markets available" />
                    <div className="text-size-sm text-gray-3 font-semibold">
                      No Markets Available
                    </div>
                  </div>
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}
