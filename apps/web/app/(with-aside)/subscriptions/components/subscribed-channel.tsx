'use client';

import SvgIcon from '@/components/icons/svg-icon';
import CommonAvatar from '@/components/ui/avatar-image';
import { UserSubscriptionsChannelProps } from '@/hooks/query/user/use-user-subscriptions';
import { Switch } from '@repo/ui/components/switch';
import Link from 'next/link';
import { useState } from 'react';

interface SubscribedChannelProps {
  channel: UserSubscriptionsChannelProps;
  socialLinks: {
    name: string;
    url: string;
  }[];
  onSubscriptionChange?: (id: string, isSubscribed: boolean) => void;
}

export default function SubscribedChannel({
  channel,
  socialLinks,
  onSubscriptionChange,
}: SubscribedChannelProps) {
  const { channelId, channelName, channelImageUrl, channelLeaderNickname, channelSubscribers } =
    channel;

  const [subscribed, setSubscribed] = useState(channel.isSubscribed);

  const handleSubscriptionChange = (checked: boolean) => {
    setSubscribed(checked);
    if (onSubscriptionChange) {
      onSubscriptionChange(channelId, checked);
    }
  };

  return (
    <div className="border-line py-space-20 gap-space-60 flex items-center justify-between border-b">
      <div className="gap-space-30 flex items-center">
        <CommonAvatar
          imageUrl={channelImageUrl}
          size="md3"
          alt={channelName}
          href={`/channels/${channelId}`}
        />
        <div className="flex-1">
          {/* Channel Title */}
          <Link
            href={`/channels/${channelId}`}
            className="text-dark-deep mb-space-5 block text-lg font-bold hover:underline"
          >
            {channelName}
          </Link>
          {/* Channel Info */}
          <div className="gap-space-20 text-size-xs flex">
            {/* Username */}
            <div className="gap-space-5 flex items-center">
              <SvgIcon name="CrownIcon" />
              <span>{channelLeaderNickname}</span>
            </div>
            {/* Subscribers */}
            <div className="gap-space-5 flex items-center">
              <SvgIcon name="SubscriptionInversedIcon" />
              <span className="text-gray-3">Subscribers</span>
              <span className="text-icon-dark">{channelSubscribers.toLocaleString()}</span>
            </div>

            {/* <div className="gap-space-5 flex items-center">
              <SvgIcon name="DollarIcon" />
              <span className="text-gray-3">Total Volumne: </span>
              <span className="text-icon-dark">{totalVolume}</span>
            </div> */}

            {/* <div className="gap-space-5 flex items-center">
              <SvgIcon name="MarketInversedIcon" />
              <span className="text-gray-3">Total Markets: </span>
              <span className="text-icon-dark">{totalMarkets}</span>
            </div> */}

            <div className="gap-space-5 flex items-center">
              <Switch
                id={`subscribe-${channelId}`}
                checked={subscribed}
                onCheckedChange={handleSubscriptionChange}
                className="data-[state=checked]:bg-sky data-[state=unchecked]:bg-gray-3"
              />
              <label htmlFor={`subscribe-${channelId}`} className="text-size-xs">
                {subscribed ? 'Subscribed' : 'Subscribe'}
              </label>
            </div>
          </div>
        </div>
      </div>

      {/* Sns Button Group  */}
      <div className="gap-space-12 flex">
        {socialLinks.map(social => (
          <a
            key={social.name}
            href={social.url}
            target="_blank"
            rel="noopener noreferrer"
            className="rounded-full transition-colors hover:bg-gray-100"
            aria-label={social.name}
          >
            <SvgIcon name="DiscordIcon" className="size-[24px]" />
          </a>
        ))}
      </div>
    </div>
  );
}
