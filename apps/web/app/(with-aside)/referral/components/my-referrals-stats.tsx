'use client';
import {
  useReferralDashboard,
  useReferralBenefit,
  useReferralLeaderboard,
} from '@/hooks/query/referral';
import { pxToRem } from '@repo/ui/lib/utils';

export default function MyReferralsStats() {
  const { data: dashboardData, isLoading: isDashboardLoading } = useReferralDashboard();
  const { data: benefitData, isLoading: isBenefitLoading } = useReferralBenefit();
  useReferralLeaderboard();

  const isLoading = isDashboardLoading || isBenefitLoading;

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 2,
    }).format(amount / 100); // Assuming amounts are in cents
  };

  // Mock data for demonstration (you can replace with actual user data)
  const userReferralCode = '1234567890';
  const totalInvitees = 123;

  return (
    <section
      className="py-space-50 gap-space-30 border-b-line flex flex-col border-b"
      style={
        {
          '--value-box-height': pxToRem(50),
        } as React.CSSProperties
      }
    >
      <header>
        <h2 className="dashboard-h2">Referrals</h2>
      </header>

      <div className="gap-space-60 flex w-full">
        <div className="gap-space-30 flex flex-1">
          <div className="gap-space-10 flex flex-1 flex-col">
            <h3 className="dashboard-h3">Your code</h3>
            <div className="text-size-sm border-line px-space-15 bg-gray-2 flex h-(--value-box-height) items-center border font-semibold">
              {userReferralCode}
            </div>
          </div>
          <div className="gap-space-10 flex flex-1 flex-col">
            <h3 className="dashboard-h3">Total Invitees</h3>
            <div className="text-size-sm border-line px-space-15 bg-gray-2 flex h-(--value-box-height) items-center border font-semibold">
              {totalInvitees}
            </div>
          </div>
        </div>
        <div className="gap-space-10 flex flex-1 flex-col">
          <h3 className="dashboard-h3">Total Commissions</h3>
          <div className="text-size-lg border-line px-space-15 bg-gray-2 flex h-(--value-box-height) items-center border font-bold">
            {isLoading ? (
              <div className="h-4 w-20 animate-pulse rounded bg-gray-300"></div>
            ) : (
              dashboardData?.totalCommissionReward || 0
            )}
          </div>
        </div>
      </div>

      <div className="p-space-20 bg-gray-2 flex h-[200px] flex-col gap-[35px]">
        <div className="text-size-sm gap-space-30 flex font-semibold">
          <span className="text-sky">
            {new Date().toLocaleDateString('en-US', { month: 'long', year: 'numeric' })}
          </span>
          <div>
            Your Level&nbsp;:&nbsp;
            <span className="text-no-red">
              {isLoading ? (
                <span className="inline-block h-4 w-8 animate-pulse rounded bg-gray-300"></span>
              ) : (
                `Lv ${benefitData?.level || 1}`
              )}
            </span>
          </div>
        </div>

        <div className="flex flex-1 flex-col justify-between">
          <div className="flex items-center justify-between">
            <div className="text-size-sm text-gray-3 flex-1 text-center font-semibold">
              Commission Reward
            </div>
            <div className="text-size-sm text-gray-3 flex-1 text-center font-semibold">
              Fee Rebate
            </div>
            <div className="text-size-sm text-gray-3 flex-1 text-center font-semibold">
              Total Profit
            </div>
          </div>
          <div className="bg-line h-[1px]" />
          <div className="flex items-center justify-between">
            <div className="text-icon-dark text-size-base flex-1 text-center font-bold">
              {isLoading ? (
                <div className="mx-auto h-4 w-16 animate-pulse rounded bg-gray-300"></div>
              ) : (
                dashboardData?.totalCommissionReward || 0
              )}
            </div>
            <div className="text-icon-dark text-size-base flex-1 text-center font-bold">
              {isLoading ? (
                <div className="mx-auto h-4 w-16 animate-pulse rounded bg-gray-300"></div>
              ) : (
                dashboardData?.totalFeeRebate || 0
              )}
            </div>
            <div className="text-icon-dark text-size-base flex-1 text-center font-bold">
              {isLoading ? (
                <div className="mx-auto h-4 w-16 animate-pulse rounded bg-gray-300"></div>
              ) : (
                Number(dashboardData?.totalCommissionReward || 0) +
                Number(dashboardData?.totalFeeRebate || 0)
              )}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
