import { InfoButton } from '@/components/ui/base.button';
import { DashboardCard } from '@/components/ui/dashboard-card';

export default function MyReferralDashboard() {
  return (
    <section>
      <h2 className="dashboard-h2 pb-space-30">Dashboard</h2>
      <div className="gap-space-60 pb-space-50 border-b-line flex border-b">
        <div className="gap-space-30 flex flex-1 flex-col">
          <DashboardCard variant="info">
            <div className="text-size-sm font-semibold">Total Rewards</div>
            <div className="text-size-2xl font-bold">$15,000.23</div>
          </DashboardCard>
          <DashboardCard className="border-sky border" variant="neutral">
            <div className="flex justify-between">
              <div className="text-size-sm font-semibold">Claim Rewards</div>
            </div>
            <div className="pl-space-10 flex justify-between">
              <span className="text-size-xs text-mid-dark font-semibold">Claimable</span>
              <div className="gap-space-30 flex items-center">
                <div className="text-size-2xl font-bold">$1,000</div>
                <InfoButton>Claim</InfoButton>
              </div>
            </div>
          </DashboardCard>
        </div>
        <div className="flex-1">
          <DashboardCard className="border-sky h-full border" variant="neutral">
            <div className="text-size-sm flex justify-between font-semibold">
              <span>
                Your Level : <span className="text-red-500">Lv 3</span>
              </span>
              <span>
                Total Rebate : <span className="text-red-500">N%</span>
              </span>
            </div>
            <div className="text-size-xs text-mid-dark pb-space-30">
              Next referral level update date Apr 01 2025, 00:00 (UTC)
            </div>
            {/* Image and Commission/Rebate Details */}
            <div className="p-space-30 mb-space-30 flex items-center justify-between rounded-md bg-gray-800">
              {/* Placeholder for image */}
              <div className="text-size-xs flex h-[70px] w-[100px] items-center justify-center bg-gray-700 text-white">
                Image Placeholder
              </div>
              <div>
                <div className="text-size-sm text-sky-500">You Receive</div>
                <div className="text-size-xl font-bold text-white">10% Commission Reward</div>
                <div className="text-size-sm pt-space-10 text-red-500">Your Friends Receive</div>
                <div className="text-size-xl font-bold text-white">5% Fee Rebate</div>
              </div>
            </div>
            {/* Social Sharing Buttons */}
            <div className="gap-space-10 flex">
              <InfoButton className="flex-1">Copy Link</InfoButton>
              <InfoButton className="flex-1">Twitter</InfoButton>
              <InfoButton className="flex-1">Telegram</InfoButton>
            </div>
          </DashboardCard>
        </div>
      </div>
    </section>
  );
}
