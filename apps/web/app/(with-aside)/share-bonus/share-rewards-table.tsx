'use client';

import * as React from 'react';
import {
  ColumnDef,
  flexRender,
  getCoreRowModel,
  useReactTable,
  getSortedRowModel,
  SortingState,
} from '@tanstack/react-table';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@repo/ui/components/table';
import {
  BaseSelect,
  BaseSelectContent,
  BaseSelectItem,
  BaseSelectTrigger,
  BaseSelectValue,
} from '@/components/ui/base.select';
import { BaseButton } from '@/components/ui/base.button';
import CommonAvatar from '@/components/ui/avatar-image';

type ShareReward = {
  id: string;
  prediction: {
    id: string;
    title: string;
    avatar: string;
  };
  winnings: number;
  shareBonus: number;
  status: 'Share' | 'Completed';
};

const data: ShareReward[] = [
  {
    id: '1',
    prediction: {
      id: 'pred-1',
      title: 'Will BTC reach $100K by end of 2025?',
      avatar:
        'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcTCpWirNMRaCEzSxvk_vwb1EjIOOKE36ar5bw&s',
    },
    winnings: 150.5,
    shareBonus: 30.1,
    status: 'Share',
  },
  {
    id: '2',
    prediction: {
      id: 'pred-2',
      title: 'ETH 2.0 launch date prediction',
      avatar:
        'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcTCpWirNMRaCEzSxvk_vwb1EjIOOKE36ar5bw&s',
    },
    winnings: 75.25,
    shareBonus: 15.05,
    status: 'Completed',
  },
  {
    id: '3',
    prediction: {
      id: 'pred-3',
      title: 'Next NFT market trend prediction',
      avatar:
        'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcTCpWirNMRaCEzSxvk_vwb1EjIOOKE36ar5bw&s',
    },
    winnings: 200.0,
    shareBonus: 40.0,
    status: 'Share',
  },
];

type SortOption = 'newest' | 'value';

const sortOptions = [
  { value: 'newest', label: 'Newest' },
  { value: 'value', label: 'Value' },
] as const;

export function ShareRewardsTable() {
  const [sorting, setSorting] = React.useState<SortingState>([]);
  const [sortBy, setSortBy] = React.useState<SortOption>('newest');
  const columns = React.useMemo<ColumnDef<ShareReward>[]>(
    () => [
      {
        accessorKey: 'prediction',
        header: 'Predictions',
        size: 50, // 40% 차지
        cell: ({ row }) => (
          <div className="gap-space-8 flex items-center">
            <CommonAvatar imageUrl={row.original.prediction.avatar} size="md" alt="Prediction" />
            <span className="text-mid-dark text-size-sm font-semibold">
              {row.original.prediction.title}
            </span>
          </div>
        ),
      },
      {
        accessorKey: 'winnings',
        header: () => <div className="text-center">Winnings</div>,
        size: 20, // 20% 차지
        cell: ({ row }) => (
          <div className="text-mid-dark text-size-sm text-center font-semibold">
            ${row.original.winnings.toFixed(2)}
          </div>
        ),
      },
      {
        accessorKey: 'shareBonus',
        header: () => <div className="text-center">Share Bonus</div>,
        size: 20, // 20% 차지
        cell: ({ row }) => (
          <div className="text-mid-dark text-size-sm text-center font-semibold">
            ${row.original.shareBonus.toFixed(2)}
          </div>
        ),
      },
      {
        accessorKey: 'status',
        header: () => <div className="text-center">Share</div>,
        size: 10, // 20% 차지
        cell: ({ row }) => (
          <div className="flex justify-center">
            {row.original.status === 'Share' ? (
              <BaseButton
                variant="info"
                size="sm2"
                className="text-size-sm w-[140px]"
                onClick={() => handleShare(row.original.id)}
              >
                Share
              </BaseButton>
            ) : (
              <span className="text-size-sm text-icon-dark font-semibold">Completed</span>
            )}
          </div>
        ),
      },
    ],
    []
  );

  const sortedData = React.useMemo(() => {
    if (sortBy === 'newest') {
      return [...data];
    } else {
      // Sort by value (shareBonus) in descending order
      return [...data].sort((a, b) => b.shareBonus - a.shareBonus);
    }
  }, [sortBy]);

  const table = useReactTable({
    data: sortedData,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    onSortingChange: setSorting,
    state: {
      sorting,
    },
  });

  const handleShare = (id: string) => {
    // TODO: Implement share functionality
    console.log(`Sharing prediction ${id}`);
  };

  return (
    <div className="w-full">
      <div>
        <div className="mb-4 flex justify-start">
          <div className="flex items-center gap-2">
            <BaseSelect value={sortBy} onValueChange={v => setSortBy(v as SortOption)}>
              <BaseSelectTrigger className="h-8 w-[150px]" size="sm">
                <BaseSelectValue />
              </BaseSelectTrigger>
              <BaseSelectContent>
                {sortOptions.map(option => (
                  <BaseSelectItem key={option.value} value={option.value}>
                    {option.label}
                  </BaseSelectItem>
                ))}
              </BaseSelectContent>
            </BaseSelect>
          </div>
        </div>
        <Table className="w-full">
          <TableHeader className="h-[60px]">
            {table.getHeaderGroups().map(headerGroup => (
              <TableRow key={headerGroup.id} className="border-b-line border-b-0">
                {headerGroup.headers.map(header => (
                  <TableHead
                    className="text-size-sm text-gray-3 font-semibold"
                    key={header.id}
                    style={{ width: header.column.getSize() + '%' }}
                  >
                    {header.isPlaceholder
                      ? null
                      : flexRender(header.column.columnDef.header, header.getContext())}
                  </TableHead>
                ))}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map(row => (
                <TableRow key={row.id} className="border-0">
                  {row.getVisibleCells().map(cell => (
                    <TableCell
                      className="py-space-15"
                      key={cell.id}
                      style={{ width: cell.column.getSize() + '%' }}
                    >
                      {flexRender(cell.column.columnDef.cell, cell.getContext())}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow className="border-0">
                <TableCell colSpan={columns.length} className="h-24 text-center">
                  No results.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}
