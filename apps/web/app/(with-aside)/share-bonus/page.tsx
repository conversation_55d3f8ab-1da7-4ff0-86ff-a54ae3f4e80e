'use client';
import SvgIcon from '@/components/icons/svg-icon';
import PageContainer from '@/components/layouts/page-container';
import { BaseButton } from '@/components/ui/base.button';
import { DashboardCard } from '@/components/ui/dashboard-card';
import Link from 'next/link';
import { ShareRewardsTable } from './share-rewards-table';
import { pxToRem } from '@repo/ui/lib/utils';

export default function ShareBonusPage() {
  return (
    <PageContainer title="Share Bonus">
      <div className="flex flex-col">
        <h2 className="dashboard-h2 pb-space-30">Dashboard</h2>
        <section className="gap-space-60 pb-space-50 border-b-line flex border-b">
          <DashboardCard className="flex-1" variant="info">
            <div className="text-size-sm font-semibold">Total Claimed Rewards</div>
            <div className="flex justify-between">
              <div className="text-size-2xl font-bold">$15,000.23</div>
              <Link className="text-size-xs text-gray-2 gap-space-6 flex items-center" href={'/'}>
                Details{' '}
                <SvgIcon
                  data-label="icon"
                  name="ChevronNextIcon"
                  className="size-[8px] text-white"
                />
              </Link>
            </div>
          </DashboardCard>
          <DashboardCard className="border-sky flex-1 border" variant="neutral">
            <div className="flex justify-between">
              <div className="text-size-sm font-semibold">Claimable Rewards</div>
            </div>
            <div className="pl-space-10 flex justify-between">
              <span className="text-size-2xl text-mid-dark font-semibold">$1,0000</span>
              <BaseButton className="w-[160px]" variant="info">
                Claim
              </BaseButton>
            </div>
          </DashboardCard>
        </section>
        <section className="py-space-50 gap-space-20 flex flex-col">
          <header>
            <h2 className="dashboard-h2 pb-spacep">Share and Earn Extra Rewards!</h2>
          </header>
          {/* Unlockable rewards */}
          <div
            style={{
              height: pxToRem(100),
            }}
            className="p-space-20 border-sky ml-auto flex w-[50%] flex-col justify-between border"
          >
            <h3 className="dashboard-h3">Unlockable Rewards</h3>
            <div className="text-size-xl text-right font-bold">$700</div>
          </div>
          <div className="pt-space-20">
            <ShareRewardsTable />
          </div>
        </section>
      </div>
    </PageContainer>
  );
}
