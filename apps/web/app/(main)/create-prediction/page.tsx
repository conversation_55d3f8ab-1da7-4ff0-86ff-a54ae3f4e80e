'use client';

import { BaseButton, GreenButton, InfoButton, NeutralButton } from '@/components/ui/base.button';
import { BaseInput } from '@/components/ui/base.input';
import { DollarInput } from '@/components/ui/dollar-input';
import {
  BaseSelect,
  BaseSelectContent,
  BaseSelectItem,
  BaseSelectTrigger,
  BaseSelectValue,
} from '@/components/ui/base.select';
import { BaseTextarea } from '@/components/ui/base.textarea';
import SimpleAlert from '@/components/ui/simple.alert';
import { useCreateMarketFlow, useMarketValidate } from '@/hooks/query/market';
import { useCategories } from '@/hooks/query/category';
import { useMyUSDCBalance } from '@/hooks/use-usdc-balance';
import { toAmount } from '@/lib/format';
import { useGlobalStore } from '@/store/global.store';
import { zodResolver } from '@hookform/resolvers/zod';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@repo/ui/components/form';
import { toast } from '@repo/ui/components/sonner';
import { pxToRem } from '@repo/ui/lib/utils';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { faker } from '@faker-js/faker';
import EthicalValidationFailPopup from '@/components/ui/popup/ethical-validation-fail-popup';
import { Popup } from '@/components/ui/popup';
import DepositChannelCollateralButton from '@/components/actions/deposit-channel-collateral-button';
import { useChannelCollateral } from '@/hooks/query/channel';

const CreateMarketFormSchema = z.object({
  title: z.string().min(2).max(100),
  description: z.string().min(2).max(2000),
  imageUrl: z.string().optional(),
  predictionDeadline: z.number(),
  resultConfirmDeadline: z.number(),
  // disputedPeriod: z.enum(['30m']),
  category: z.string().min(2).max(50),
  collateralAmount: z.number().min(50),
  outcomes: z.array(z.string()).min(2).max(10),
  tags: z.array(z.string()).max(3),
  broadcastURL: z.string().optional(),
  referenceURL: z.string(),
  image: z.instanceof(File).optional(),
});

export type CreateMarketFormValues = z.infer<typeof CreateMarketFormSchema>;

const PLACEHOLDER_TEXT = {
  deposit: 'Minimum Amount $50',
  category: 'Select Category',
  tag: 'Please create tag(max. 20 characters)',
  'market-title': 'Write a prediction title.(max. 100 characters)',
  'market-outcomes': 'Write an outcome.(max. 50 characters)',
  'broadcast-url':
    'Please enter the URL of a live video related to the prediction.(Youtube, Twitch...)',
  'reference-url': 'Please enter a URL related to the prediction.',
  description:
    'Please write in detail about the explanation of predictions and the rules by which the prediction is resolved.',
  'prediction-end-time': 'YYYY-MM-DD  hh:mm',
  'confirmation-end-time': 'YYYY-MM-DD  hh:mm',
  'dispute-period': '',
};

export default function ChannelsPredictionsCreatePage() {
  const router = useRouter();
  const [tags, setTags] = useState<string[]>([]);
  const [currentTag, setCurrentTag] = useState('');
  const [imageFile, setImageFile] = useState<File | null>(null);
  const [imagePreview, setImagePreview] = useState<string>('');
  const [validationId, setValidationId] = useState<string | null>(null);
  const [titleValidationError, setTitleValidationError] = useState<boolean>(false);
  const [showEthicalFailPopup, setShowEthicalFailPopup] = useState<boolean>(false);
  const { data: collateral, isLoading: isCollateralLoading } = useChannelCollateral();

  const { safeSmartAccount } = useGlobalStore();
  const { balance: usdcBalance, isLoading: isBalanceLoading } = useMyUSDCBalance();
  const { data: categories, isLoading: isCategoriesLoading } = useCategories();

  const marketValidate = useMarketValidate();

  const createMarketTransaction = useCreateMarketFlow({
    onSuccess: result => {
      toast.success('마켓이 성공적으로 생성되었습니다!');
      router.push(`/markets/${result.id}`); // 필요시 리다이렉트
    },
    onError: (error, phase) => {
      if (phase === 'signing') {
        toast.error('서명에 실패했습니다: ' + error.message);
      } else {
        toast.error('마켓 생성에 실패했습니다: ' + error.message);
      }
    },
  });

  const form = useForm<CreateMarketFormValues>({
    resolver: zodResolver(CreateMarketFormSchema),
    defaultValues: {
      title: '',
      description: '',
      imageUrl: '',
      predictionDeadline: 0,
      resultConfirmDeadline: 0,
      category: '',
      collateralAmount: undefined,
      outcomes: ['', ''],
      tags: [],
      broadcastURL: '',
      referenceURL: '',
    },
  });

  const { watch, setValue } = form;
  const outcomes = watch('outcomes');

  const addOutcome = () => {
    const currentOutcomes = watch('outcomes');
    if (currentOutcomes.length < 10) {
      setValue('outcomes', [...currentOutcomes, '']);
    }
  };

  const removeOutcome = (index: number) => {
    const currentOutcomes = watch('outcomes');
    if (currentOutcomes.length > 2) {
      const newOutcomes = currentOutcomes.filter((_, i) => i !== index);
      setValue('outcomes', newOutcomes);
    }
  };

  const updateOutcome = (index: number, value: string) => {
    const currentOutcomes = watch('outcomes');
    const newOutcomes = [...currentOutcomes];
    newOutcomes[index] = value;
    setValue('outcomes', newOutcomes);
  };

  const addTag = () => {
    if (currentTag.trim() && tags.length < 3 && !tags.includes(currentTag.trim())) {
      const newTags = [...tags, currentTag.trim()];
      setTags(newTags);
      setValue('tags', newTags);
      setCurrentTag('');
    }
  };

  const removeTag = (tagToRemove: string) => {
    const newTags = tags.filter(tag => tag !== tagToRemove);
    setTags(newTags);
    setValue('tags', newTags);
  };

  const handleTagKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      addTag();
    }
  };

  const handleEthicalReview = async (e: React.MouseEvent<HTMLButtonElement>) => {
    e.preventDefault();
    const title = form.getValues('title');
    if (!title.trim()) {
      toast.error('제목을 입력해주세요.');
      return;
    }

    try {
      setTitleValidationError(false);
      setShowEthicalFailPopup(false); // 팝업 닫기 시도
      const validationResult = await marketValidate.mutateAsync({
        title: title,
      });

      if (!validationResult.isEthical) {
        // toast.error('이 마켓은 윤리적 기준에 맞지 않습니다.');
        setTitleValidationError(true);
        setShowEthicalFailPopup(true); // 팝업 열기
        return;
      }

      if (!validationResult.validationId) {
        toast.error('검증 ID를 받을 수 없습니다.');
        setTitleValidationError(true);
        return;
      }

      setValidationId(validationResult.validationId);
      toast.success('윤리적 검토가 완료되었습니다.');
    } catch (error) {
      console.error('Ethical review failed:', error);
      setTitleValidationError(true);
      if (error instanceof Error) {
        toast.error('윤리적 검토에 실패했습니다: ' + error.message);
      } else {
        toast.error('윤리적 검토에 실패했습니다.');
      }
    }
  };

  const handleEthicalFailPopupClose = () => {
    setShowEthicalFailPopup(false);
    setTitleValidationError(true); // 팝업 닫을 때도 에러 상태 유지
    setValidationId(null); // 검증 ID 초기화
  };

  // 개발용 자동 데이터 채우기 함수
  const fillDevData = () => {
    if (process.env.NODE_ENV !== 'development') return;

    const now = new Date();

    const twoMinutesLater = new Date(now.getTime() + 2 * 60 * 1000);
    const fourMinutesLater = new Date(now.getTime() + 20 * 60 * 1000);

    const predictionDeadlineTimestamp = Math.floor(twoMinutesLater.getTime() / 1000);
    const resultConfirmDeadlineTimestamp = Math.floor(fourMinutesLater.getTime() / 1000);

    // 랜덤 예측 주제 생성
    const predictionTopics = [
      {
        subject: faker.company.name(),
        context: 'stock price',
        threshold: faker.number.int({ min: 50, max: 500 }),
        outcomes: ['Above', 'Below'],
      },
      {
        subject: faker.location.country(),
        context: 'next election winner',
        threshold: null,
        outcomes: [faker.person.lastName(), faker.person.lastName()],
      },
      {
        subject: faker.finance.currencyName(),
        context: 'price',
        threshold: faker.number.float({ min: 0.1, max: 10, fractionDigits: 2 }),
        outcomes: ['Higher', 'Lower'],
      },
      {
        subject: faker.science.chemicalElement().name,
        context: 'discovery announcement',
        threshold: null,
        outcomes: ['Yes', 'No'],
      },
      {
        subject: faker.animal.type(),
        context: 'population increase',
        threshold: faker.number.int({ min: 5, max: 50 }),
        outcomes: ['Increase', 'Decrease', 'Stable'],
      },
    ];

    const randomTopic = faker.helpers.arrayElement(predictionTopics);

    // 랜덤 타이틀 생성
    let title = '';
    if (randomTopic.threshold) {
      title = `Will ${randomTopic.subject} ${randomTopic.context} reach ${randomTopic.threshold} by ${faker.date.future({ years: 1 }).getFullYear()}?`;
    } else {
      title = `Will ${randomTopic.subject} ${randomTopic.context} happen in ${faker.date.future({ years: 1 }).getFullYear()}?`;
    }

    // 랜덤 설명 생성
    const description = `${faker.lorem.paragraph(3)} ${faker.lorem.sentence()} The outcome will be determined based on ${faker.helpers.arrayElement(
      [
        'official announcements from relevant authorities',
        'market data from major exchanges',
        'verified news sources and official reports',
        'government statistics and official publications',
        'industry reports and verified measurements',
      ]
    )}. ${faker.lorem.sentence()} ${faker.lorem.sentence()}`;

    // 랜덤 참조 URL 생성
    const referenceUrls = [
      'https://coinmarketcap.com',
      'https://www.bloomberg.com',
      'https://finance.yahoo.com',
      'https://www.reuters.com',
      'https://www.bbc.com/news',
      'https://www.cnn.com',
      'https://www.wikipedia.org',
    ];

    // 폼 데이터 설정
    form.setValue('title', title);
    form.setValue('description', description);
    form.setValue('collateralAmount', 100);
    form.setValue('category', 'Test');
    form.setValue('outcomes', randomTopic.outcomes);
    form.setValue('tags', []);
    form.setValue('referenceURL', faker.helpers.arrayElement(referenceUrls));
    form.setValue('predictionDeadline', predictionDeadlineTimestamp);
    form.setValue('resultConfirmDeadline', resultConfirmDeadlineTimestamp);
    form.setValue('broadcastURL', '');

    // 태그 상태도 초기화
    setTags([]);
    setCurrentTag('');
    // 검증 상태 초기화
    setValidationId(null);
    setTitleValidationError(false);
  };

  const watched = watch();
  console.log('watched', watched);

  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    if (!file.type.startsWith('image/')) {
      toast.error('이미지 파일만 업로드할 수 있습니다.');
      return;
    }

    if (file.size > 1 * 1024 * 1024) {
      toast.error('파일 크기는 1MB를 초과할 수 없습니다.');
      return;
    }

    setImageFile(file);
    const reader = new FileReader();
    reader.onload = e => {
      const result = e.target?.result as string;
      setImagePreview(result);
    };
    reader.readAsDataURL(file);
  };

  console.log('imageFile', imageFile);

  async function onSubmit(values: CreateMarketFormValues) {
    if (!safeSmartAccount) {
      return;
    }

    // Check if collateralAmount exceeds 2x USDC balance
    const maxAllowedAmount = parseFloat(usdcBalance) * 2;
    if (values.collateralAmount > maxAllowedAmount) {
      toast.error(
        `담보금은 잔액의 2배(${maxAllowedAmount.toLocaleString()})를 초과할 수 없습니다.`
      );
      return;
    }

    // validationId가 없으면 에러
    if (!validationId) {
      toast.error('윤리적 검토를 먼저 완료해주세요.');
      return;
    }

    try {
      const filteredData = {
        ...values,
        outcomes: values.outcomes.filter(outcome => outcome.trim() !== ''),
        tags: values.tags.filter(tag => tag.trim() !== ''),
      };

      const reqData = {
        maker: safeSmartAccount.address,
        channelId: safeSmartAccount.address,
        title: filteredData.title,
        description: filteredData.description,
        predictionDeadline: filteredData.predictionDeadline * 1000,
        resultConfirmDeadline: filteredData.resultConfirmDeadline * 1000,
        disputedPeriod: '30m',
        category: filteredData.category,
        collateralAmount: BigInt(toAmount(filteredData.collateralAmount)),
        outcomes: filteredData.outcomes,
        tags: filteredData.tags,
        referenceURL: filteredData.referenceURL,
        validationId: validationId,
      };

      await createMarketTransaction.mutateAsync({
        ...reqData,
        image: imageFile || undefined,
      });
    } catch (error) {
      console.error('Failed to create market:', error);
      if (error instanceof Error) {
        toast.error('마켓 생성에 실패했습니다: ' + error.message);
      }
    }
  }

  return (
    <div className="page p-4">
      {process.env.NODE_ENV === 'development' && (
        <div className="mb-4 flex justify-end">
          <BaseButton
            type="button"
            variant="outline"
            size="sm"
            onClick={fillDevData}
            className="border-yellow-300 bg-yellow-100 text-yellow-800 hover:bg-yellow-200"
          >
            🎲 Fill Random Data
          </BaseButton>
        </div>
      )}
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="gap-space-30 flex w-full flex-col">
          <section data-role="section" className="gap-space-30 flex items-center">
            <button
              type="button"
              data-role="upload-image"
              className="relative size-[80px]"
              onClick={() => document.getElementById('image-upload')?.click()}
            >
              <div
                className="bg-gray-2 border-line h-full w-full rounded-full border"
                style={{
                  backgroundSize: 'cover',
                  backgroundPosition: 'center',
                  backgroundImage: `url('${imagePreview}')`,
                }}
              ></div>
              <div
                style={{
                  right: '0px',
                  bottom: '0px',
                }}
                className="bg-sky absolute flex size-[24px] items-center justify-center rounded-full"
              >
                <svg
                  width="10"
                  height="10"
                  viewBox="0 0 10 10"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M3.86229 9.5V0.5H6.13771V9.5H3.86229ZM0.5 6.13771V3.86229H9.5V6.13771H0.5Z"
                    fill="white"
                  />
                </svg>
              </div>
              <input
                id="image-upload"
                type="file"
                accept="image/*"
                onChange={handleImageUpload}
                className="hidden"
              />
            </button>
            <div className="text-sm text-gray-500">
              <div className="mb-1">
                Please upload a representative image for the prediction to be generated. If not
                uploaded, it will be replaced with a default image.
              </div>
              <div>
                At least <b>80px X 80px</b> recommended.
                <br />
                <b>JPG or PNG</b> is allowed. File size up to <b>1MB.</b>
              </div>
            </div>
          </section>

          <section data-role="section" className="gap-space-30 flex items-start">
            <div className="flex flex-[0.25] flex-col">
              <FormField
                control={form.control}
                name="collateralAmount"
                render={({ field }) => (
                  <FormItem data-role="write-deposit" className="space-y-3">
                    <div className="flex items-center justify-between">
                      <FormLabel className="block text-sm font-semibold">Deposit</FormLabel>
                      <div className="gap-space-10 flex items-center">
                        <div className="text-size-xxs text-gray-3 font-medium">
                          Deposit Balance ${!collateral ? '...' : collateral?.available}
                        </div>
                        <DepositChannelCollateralButton
                          style={{
                            height: '20px',
                            borderRadius: '4px',
                            fontSize: '10px',
                            width: pxToRem(80),
                          }}
                          variant="dark"
                        >
                          Add Deposit
                        </DepositChannelCollateralButton>
                      </div>
                    </div>
                    <FormControl>
                      <DollarInput
                        placeholder={PLACEHOLDER_TEXT.deposit}
                        value={field.value || 0}
                        onChange={field.onChange}
                        maxValue={parseFloat(usdcBalance) * 2}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <div className="py-space-10 px-[5px]">
                <SimpleAlert description="Twice the set deposit amount is the maximum volume for each option." />
              </div>
            </div>

            <FormField
              control={form.control}
              name="category"
              render={({ field }) => (
                <FormItem data-role="select-category" className="flex-[0.25] space-y-3">
                  <FormLabel className="block text-sm font-semibold">Category</FormLabel>
                  <FormControl>
                    <BaseSelect onValueChange={field.onChange} value={field.value}>
                      <BaseSelectTrigger className="w-full">
                        <BaseSelectValue placeholder={PLACEHOLDER_TEXT.category} />
                      </BaseSelectTrigger>
                      <BaseSelectContent>
                        {isCategoriesLoading ? (
                          <BaseSelectItem value="undefined" disabled>
                            Loading categories...
                          </BaseSelectItem>
                        ) : (
                          categories?.map(category => (
                            <BaseSelectItem
                              key={category.categoryName}
                              value={category.categoryName}
                            >
                              {category.categoryName}
                            </BaseSelectItem>
                          ))
                        )}
                      </BaseSelectContent>
                    </BaseSelect>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="flex flex-[0.5] flex-col">
              <div data-role="write-tag" className="space-y-3">
                <div className="flex items-center">
                  <label className="block text-sm font-semibold">Tags</label>
                  &nbsp;
                  <span className="text-size-xs text-yes-green font-medium">
                    (Up to 3 tags allowed)
                  </span>
                </div>
                <BaseInput
                  placeholder={PLACEHOLDER_TEXT.tag}
                  value={currentTag}
                  onChange={e => setCurrentTag(e.target.value)}
                  onKeyPress={handleTagKeyPress}
                  disabled={tags.length >= 3}
                  maxLength={20}
                />
                <div className="flex flex-wrap gap-2">
                  {tags.map((tag, index) => (
                    <div
                      key={index}
                      className="rounded-round-md px-space-10 bg-gray-1 border-line flex h-(--tag-height) items-center border"
                    >
                      <span className="text-size-xs text-gray-3 mr-2">{tag}</span>
                      <button
                        type="button"
                        onClick={() => removeTag(tag)}
                        className="text-gray-500 hover:text-gray-700"
                        aria-label="Remove tag"
                      >
                        <svg
                          width="12"
                          height="12"
                          viewBox="0 0 12 12"
                          fill="none"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M9 3L3 9M9 9L3 3"
                            stroke="#3B424B"
                            strokeWidth="2"
                            strokeLinecap="round"
                          />
                        </svg>
                      </button>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </section>

          <section data-role="section" className="">
            <header className="mb-space-20 text-size-sm flex items-center justify-between font-medium">
              <div>Quest</div>
              <div className="gap-space-10 flex items-center">
                <p className="text-size-xs text-yes-green">
                  The Invalid Outcome is automatically added during the resolution phase, so you
                  don't need to add it manually.
                </p>
                <NeutralButton type="button">Learn More</NeutralButton>
              </div>
            </header>
            <div className="space-y-4">
              <div className="gap-space-20 flex items-center justify-between">
                <FormField
                  control={form.control}
                  name="title"
                  render={({ field }) => (
                    <FormItem data-role="write-market-title" className="flex-1 space-y-2">
                      <FormControl>
                        <BaseInput
                          placeholder={PLACEHOLDER_TEXT['market-title']}
                          {...field}
                          className={
                            titleValidationError ? 'border-red-500 focus:border-red-500' : ''
                          }
                          onChange={e => {
                            field.onChange(e);
                            if (titleValidationError) {
                              setTitleValidationError(false);
                            }
                            if (validationId) {
                              setValidationId(null);
                            }
                          }}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <GreenButton
                  type="button"
                  onClick={handleEthicalReview}
                  disabled={marketValidate.isPending || !!validationId}
                >
                  {marketValidate.isPending ? (
                    <div className="flex items-center gap-2">
                      <div className="h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent"></div>
                      Reviewing...
                    </div>
                  ) : validationId ? (
                    <div className="flex items-center gap-2">
                      <svg
                        width="16"
                        height="16"
                        viewBox="0 0 24 24"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M20 6L9 17L4 12"
                          stroke="currentColor"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        />
                      </svg>
                      Reviewed
                    </div>
                  ) : (
                    'Ethical Review'
                  )}
                </GreenButton>
              </div>
              <div data-role="market-outcomes" className="gap-space-10 flex flex-col">
                <div className="gap-space-10 flex flex-col">
                  {outcomes.map((outcome, index) => (
                    <div key={index} className="gap-space-10 flex items-center">
                      <div className="text-size-xs font-semibold">{index + 1}</div>
                      <div className="relative flex-1">
                        <BaseInput
                          className="pr-10"
                          placeholder={PLACEHOLDER_TEXT['market-outcomes']}
                          value={outcome}
                          onChange={e => updateOutcome(index, e.target.value)}
                        />
                        {outcomes.length > 2 && (
                          <button
                            type="button"
                            onClick={() => removeOutcome(index)}
                            className="absolute top-1/2 right-2 -translate-y-1/2 rounded-sm p-1 hover:bg-gray-100"
                          >
                            <svg
                              width="16"
                              height="16"
                              viewBox="0 0 24 24"
                              fill="none"
                              xmlns="http://www.w3.org/2000/svg"
                            >
                              <path
                                d="M15.7123 9.34835C16.0052 9.05546 16.0052 8.58058 15.7123 8.28769C15.4194 7.9948 14.9445 7.9948 14.6517 8.28769L15.7123 9.34835ZM8.28769 14.6517C7.9948 14.9445 7.9948 15.4194 8.28769 15.7123C8.58058 16.0052 9.05546 16.0052 9.34835 15.7123L8.28769 14.6517ZM14.6517 15.7123C14.9445 16.0052 15.4194 16.0052 15.7123 15.7123C16.0052 15.4194 16.0052 14.9445 15.7123 14.6517L14.6517 15.7123ZM9.34835 8.28769C9.05546 7.9948 8.58058 7.9948 8.28769 8.28769C7.9948 8.58058 7.9948 9.05546 8.28769 9.34835L9.34835 8.28769ZM21 12H20.25C20.25 16.5563 16.5563 20.25 12 20.25V21V21.75C17.3848 21.75 21.75 17.3848 21.75 12H21ZM12 21V20.25C7.44365 20.25 3.75 16.5563 3.75 12H3H2.25C2.25 17.3848 6.61522 21.75 12 21.75V21ZM3 12H3.75C3.75 7.44365 7.44365 3.75 12 3.75V3V2.25C6.61522 2.25 2.25 6.61522 2.25 12H3ZM12 3V3.75C16.5563 3.75 20.25 7.44365 20.25 12H21H21.75C21.75 6.61522 17.3848 2.25 12 2.25V3ZM15.182 8.81802L14.6517 8.28769L11.4697 11.4697L12 12L12.5303 12.5303L15.7123 9.34835L15.182 8.81802ZM12 12L11.4697 11.4697L8.28769 14.6517L8.81802 15.182L9.34835 15.7123L12.5303 12.5303L12 12ZM15.182 15.182L15.7123 14.6517L12.5303 11.4697L12 12L11.4697 12.5303L14.6517 15.7123L15.182 15.182ZM12 12L12.5303 11.4697L9.34835 8.28769L8.81802 8.81802L8.28769 9.34835L11.4697 12.5303L12 12Z"
                                fill="#8E8E93"
                              />
                            </svg>
                          </button>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
                {outcomes.length < 10 && (
                  <BaseButton
                    type="button"
                    variant="neutral"
                    size="sm"
                    className="text-size-xs px-space-10"
                    onClick={addOutcome}
                  >
                    <div>
                      <svg
                        width="20"
                        height="20"
                        viewBox="0 0 20 20"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M2.76923 2.5H11.2308C11.6556 2.5 12 2.88376 12 3.35714V7.64286C12 8.11624 11.6556 8.5 11.2308 8.5H2.76923C2.3444 8.5 2 8.11624 2 7.64286V3.35714C2 2.88376 2.3444 2.5 2.76923 2.5Z"
                          fill="#3B424B"
                          stroke="#3B424B"
                          strokeWidth="1.5"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        />
                        <path
                          d="M15 14.5003H16.5M18 14.5003H16.5M16.5 14.5003V16M16.5 14.5003V13"
                          stroke="#3B424B"
                          strokeWidth="1.5"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        />
                        <path
                          d="M11.2308 11.5H2.76923C2.3444 11.5 2 11.8838 2 12.3571V16.6429C2 17.1162 2.3444 17.5 2.76923 17.5H11.2308C11.6556 17.5 12 17.1162 12 16.6429V12.3571C12 11.8838 11.6556 11.5 11.2308 11.5Z"
                          stroke="#3B424B"
                          strokeWidth="1.5"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeDasharray="2 2"
                        />
                      </svg>
                    </div>
                    Add Outcome
                  </BaseButton>
                )}
              </div>
            </div>
          </section>

          <section data-role="section" className="space-y-space-30">
            <FormField
              control={form.control}
              name="broadcastURL"
              render={({ field }) => (
                <FormItem data-role="write-broadcast-url" className="space-y-space-15">
                  <div className="flex items-center">
                    <FormLabel className="text-sm font-semibold">Broadcast Video URL</FormLabel>
                    &nbsp;
                    <span className="text-yes-green text-size-xs">(Optional)</span>
                  </div>
                  <FormControl>
                    <BaseInput placeholder={PLACEHOLDER_TEXT['broadcast-url']} {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="referenceURL"
              render={({ field }) => (
                <FormItem data-role="write-reference-url" className="space-y-space-15">
                  <FormLabel className="block text-sm font-semibold">Reference URL</FormLabel>
                  <FormControl>
                    <BaseInput placeholder={PLACEHOLDER_TEXT['reference-url']} {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem data-role="write-description" className="space-y-space-15">
                  <FormLabel className="block text-sm font-semibold">Description</FormLabel>
                  <FormControl>
                    <BaseTextarea placeholder={PLACEHOLDER_TEXT.description} {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </section>

          <section data-role="section" className="gap-space-30 flex items-start">
            <FormField
              control={form.control}
              name="predictionDeadline"
              render={({ field }) => (
                <FormItem
                  data-role="select-prediction-end-time"
                  className="space-y-space-15 w-full max-w-[320px]"
                >
                  <FormLabel className="block text-sm font-medium">Prediction end</FormLabel>
                  <FormControl>
                    <BaseInput
                      type="datetime-local"
                      placeholder={PLACEHOLDER_TEXT['prediction-end-time']}
                      value={
                        field.value ? new Date(field.value * 1000).toISOString().slice(0, 16) : ''
                      }
                      onChange={e => {
                        const dateValue = e.target.value;
                        if (dateValue) {
                          const ms = Math.floor(new Date(dateValue).getTime() / 1000);
                          field.onChange(ms);
                        } else {
                          field.onChange(0);
                        }
                      }}
                    />
                  </FormControl>
                  <SimpleAlert description="Prediction is not possible after the set time has passed." />
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="resultConfirmDeadline"
              render={({ field }) => (
                <FormItem
                  data-role="select-confirmation-end-time"
                  className="space-y-space-15 w-full max-w-[320px]"
                >
                  <FormLabel className="block text-sm font-medium">
                    Result confirmation end
                  </FormLabel>
                  <FormControl>
                    <BaseInput
                      type="datetime-local"
                      placeholder={PLACEHOLDER_TEXT['confirmation-end-time']}
                      value={
                        field.value ? new Date(field.value * 1000).toISOString().slice(0, 16) : ''
                      }
                      onChange={e => {
                        const dateValue = e.target.value;
                        if (dateValue) {
                          const timestamp = Math.floor(new Date(dateValue).getTime() / 1000);
                          field.onChange(timestamp);
                        } else {
                          field.onChange(0);
                        }
                      }}
                    />
                  </FormControl>
                  <SimpleAlert description="If the result are not confirmed by this time, it will automatically be set to 'Invalid,' and you may lose your deposit." />
                  <FormMessage />
                </FormItem>
              )}
            />

            <div
              data-role="select-dispute-period"
              className="space-y-space-15 w-full max-w-[340px]"
            >
              <label className="block text-sm font-medium">Disputed Period</label>
              <div className="w-full">
                <div className="rounded-md border border-gray-300 bg-gray-50 px-3 py-2 text-sm text-gray-500">
                  30분 (고정값)
                </div>
              </div>
              <SimpleAlert description="After the verifier confirms the result, it is the time during which an objection to the result can be filed. Users who wish to raise an objection must deposit a bond equal to the verifier's bond. If the dispute is upheld, the channel leader must determine the final result." />
            </div>
          </section>

          <div className="mt-8 flex space-x-4">
            <InfoButton
              type="submit"
              size="lg"
              fontSize="base"
              width={pxToRem(256)}
              disabled={createMarketTransaction.isPending}
            >
              {createMarketTransaction.isPending ? 'Creating...' : 'Create Prediction'}
            </InfoButton>
          </div>
        </form>
      </Form>

      {showEthicalFailPopup && (
        <Popup isOpen={showEthicalFailPopup} onClose={handleEthicalFailPopupClose}>
          <EthicalValidationFailPopup onEditQuestion={handleEthicalFailPopupClose} />
        </Popup>
      )}
    </div>
  );
}
