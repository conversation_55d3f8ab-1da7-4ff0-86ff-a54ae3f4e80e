'use client';

import SvgIcon from '@/components/icons/svg-icon';
import {
  BaseSelect,
  BaseSelectContent,
  BaseSelectItem,
  BaseSelectTrigger,
  BaseSelectValue,
} from '@/components/ui/base.select';
import { useEffect, useState } from 'react';
import { LeaderboardPeriod } from '@/lib/api/leaderboard/leaderboard.schema';
import VolumeColumn from './volume-column';
import ProfitColumn from './profit-column';

// 정렬 옵션 목록
const sortOptions = [
  { value: 'day', label: 'Day' },
  { value: 'week', label: 'Week' },
  { value: 'all', label: 'All Time' },
] as const;

// 카운트다운 계산 함수
const useCountdown = (onComplete?: () => void) => {
  const [countdown, setCountdown] = useState<string>('');

  useEffect(() => {
    const calculateTimeLeft = () => {
      const now = new Date();
      const tomorrow = new Date();
      tomorrow.setUTCHours(24, 0, 0, 0); // 다음날 UTC 00:00

      const difference = tomorrow.getTime() - now.getTime();

      // 카운트다운이 0이 되면 데이터 갱신
      if (difference <= 0 && onComplete) {
        onComplete();
        // 다음 날로 설정
        tomorrow.setDate(tomorrow.getDate() + 1);
        tomorrow.setUTCHours(24, 0, 0, 0);
      }

      const hours = Math.floor((difference % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
      const minutes = Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60));
      const seconds = Math.floor((difference % (1000 * 60)) / 1000);

      return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    };

    // 초기 카운트다운 설정
    setCountdown(calculateTimeLeft());

    // 1초마다 카운트다운 업데이트
    const timer = setInterval(() => {
      setCountdown(calculateTimeLeft());
    }, 1000);

    return () => clearInterval(timer);
  }, [onComplete]);

  return countdown;
};

export default function RankPredictLeaderboardTab() {
  const [sortPeriod, setSortPeriod] = useState<LeaderboardPeriod>('day');
  const countdown = useCountdown();

  return (
    <div className="mt-space-30">
      {/* 정렬 기준 및 카운트다운 */}
      <div className="mb-space-20 flex items-center justify-between">
        <BaseSelect
          value={sortPeriod}
          onValueChange={value => setSortPeriod(value as LeaderboardPeriod)}
        >
          <BaseSelectTrigger className="w-[150px]">
            <BaseSelectValue placeholder="Sort by" />
          </BaseSelectTrigger>
          <BaseSelectContent>
            {sortOptions.map(option => (
              <BaseSelectItem key={option.value} value={option.value}>
                {option.label}
              </BaseSelectItem>
            ))}
          </BaseSelectContent>
        </BaseSelect>
        <div className="gap-space-6 text-size-sm flex items-center">
          <SvgIcon name="ClockIcon" className="text-red-500" />
          <span>{countdown}</span>
        </div>
      </div>

      {/* Volume과 Profit 컬럼 */}
      <div className="gap-space-30 flex">
        <VolumeColumn period={sortPeriod} />
        <ProfitColumn period={sortPeriod} />
      </div>
    </div>
  );
}
