'use client';

import { useVolumeLeaderboard, LeaderboardItem } from '@/hooks/query/leaderboard/use-leaderboard';
import { LeaderboardPeriod } from '@/lib/api/leaderboard/leaderboard.schema';
import RankUser from './RankUser';

interface VolumeColumnProps {
  period: LeaderboardPeriod;
}

export default function VolumeColumn({ period }: VolumeColumnProps) {
  const { data: volumeUsers = [], isLoading } = useVolumeLeaderboard(period);

  if (isLoading) {
    return (
      <div className="flex-1 overflow-hidden">
        <div className="p-space-15 gap-space-6 border-line flex items-center border-b">
          <h3 className="font-semibold">Volume</h3>
        </div>
        <div className="p-space-15">
          <div className="text-center text-gray-500">Loading...</div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex-1 overflow-hidden">
      <div className="p-space-15 gap-space-6 border-line flex items-center border-b">
        <h3 className="font-semibold">Volume</h3>
      </div>

      <div className="p-space-15 space-y-space-15">
        {volumeUsers.map(item => (
          <RankUser
            key={item.userAddress}
            user={{
              id: item.userAddress,
              username: item.userNickname,
              avatarUrl: item.userAvatarUrl || '',
              volume: parseFloat(item.userValue.replace(/[$,]/g, '')),
              profit: 0,
            }}
            rank={item.userRank}
            amount={parseFloat(item.userValue.replace(/[$,]/g, ''))}
          />
        ))}
      </div>
    </div>
  );
}
