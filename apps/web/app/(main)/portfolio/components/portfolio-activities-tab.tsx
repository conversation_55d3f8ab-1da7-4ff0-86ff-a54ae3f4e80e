'use client';

import CommonAvatar from '@/components/ui/avatar-image';
import {
  BaseSelect,
  BaseSelectContent,
  BaseSelectItem,
  BaseSelectTrigger,
  BaseSelectValue,
} from '@/components/ui/base.select';
import PredictionLabel from '@/components/ui/prediction-label';
import { PortfolioActivityItem } from '@/hooks/query/portfolio/use-portfolio-activities';
import { usePortfolioActivities } from '@/hooks/query/portfolio';
import { ActivityFilter, ActivityOrder } from '@/lib/api/portfolio/portfolio.schema';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@repo/ui/components/table';
import { cn } from '@repo/ui/lib/utils';
import { ColumnDef, flexRender, getCoreRowModel, useReactTable } from '@tanstack/react-table';
import { ExternalLink } from 'lucide-react';
import { useState } from 'react';

const filterOptions = [
  // { value: 'All' as ActivityFilter, label: 'All' },
  { value: 'Predict' as ActivityFilter, label: 'Predict' },
  { value: 'Redeem' as ActivityFilter, label: 'Redeem' },
  { value: 'Void' as ActivityFilter, label: 'Void' },
];

const sortOptions = [
  { value: 'newest' as ActivityOrder, label: 'Newest' },
  { value: 'oldest' as ActivityOrder, label: 'Oldest' },
];

export const columns: ColumnDef<PortfolioActivityItem>[] = [
  {
    accessorKey: 'marketTitle',
    header: 'Market',
    size: 1,
    cell: ({ row }) => {
      const activity = row.original;
      return (
        <div className="gap-space-20 flex items-center justify-start">
          <CommonAvatar size="md2" imageUrl={activity.marketImageUrl || ''} />
          <div className="flex flex-col">
            <div className="text-mid-dark text-size-sm font-semibold">{activity.marketTitle}</div>
            <div className="gap-space-10 flex items-center">
              <PredictionLabel label={activity.outcome} colorName={activity.outcomeOrder} />
            </div>
          </div>
        </div>
      );
    },
  },
  {
    accessorKey: 'activityType',
    header: ({ column }) => <div className="text-center">Type</div>,
    size: 1,
    cell: ({ row }) => (
      <div className="text-size-sm text-center font-medium">{row.original.activityType}</div>
    ),
  },
  {
    accessorKey: 'value',
    header: ({ column }) => <div className="text-right">Value</div>,
    size: 1,
    cell: ({ row }) => (
      <div className="flex flex-col items-end">
        <div className="text-size-sm text-gray-600">${row.original.activityAmount}</div>
        <div className="gap-space-8 flex items-center">
          <span className="text-size-xs text-gray-3">{row.original.relativeTime}</span>
          <a href={row.original.txUrl} target="_blank" rel="noopener noreferrer">
            <ExternalLink size={12} />
          </a>
        </div>
      </div>
    ),
  },
];

export default function PortfolioActivityTab() {
  const [filter, setFilter] = useState<ActivityFilter>('Predict');
  const [sortBy, setSortBy] = useState<ActivityOrder>('newest');
  const [page, setPage] = useState(0);
  const [rowSelection, setRowSelection] = useState({});

  const {
    data: activitiesData,
    isLoading,
    error,
  } = usePortfolioActivities({
    page,
    limit: 20,
    filter,
    order: sortBy,
  });

  const activities = activitiesData?.activities || [];

  const table = useReactTable<PortfolioActivityItem>({
    data: activities,
    columns,
    getCoreRowModel: getCoreRowModel(),
    onRowSelectionChange: setRowSelection,
    state: {
      rowSelection,
    },
  });

  if (isLoading && page === 0) {
    return (
      <div className={cn('mt-space-30')}>
        <div className="flex items-center justify-center py-20">
          <div className="text-gray-600">Loading activities...</div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={cn('mt-space-30')}>
        <div className="flex items-center justify-center py-20">
          <div className="text-red-600">Failed to load activities</div>
        </div>
      </div>
    );
  }

  return (
    <div className={cn('mt-space-30')}>
      {/* 필터 영역 */}
      <div className="mb-space-20 gap-space-10 flex">
        {/* 타입 필터 */}
        <BaseSelect value={filter} onValueChange={value => setFilter(value as ActivityFilter)}>
          <BaseSelectTrigger className="w-[150px]">
            <BaseSelectValue placeholder="Filter" />
          </BaseSelectTrigger>
          <BaseSelectContent>
            {filterOptions.map(option => (
              <BaseSelectItem key={option.value} value={option.value}>
                {option.label}
              </BaseSelectItem>
            ))}
          </BaseSelectContent>
        </BaseSelect>

        {/* 정렬 필터 */}
        <BaseSelect value={sortBy} onValueChange={value => setSortBy(value as ActivityOrder)}>
          <BaseSelectTrigger className="w-[150px]">
            <BaseSelectValue placeholder="Sort by" />
          </BaseSelectTrigger>
          <BaseSelectContent>
            {sortOptions.map(option => (
              <BaseSelectItem key={option.value} value={option.value}>
                {option.label}
              </BaseSelectItem>
            ))}
          </BaseSelectContent>
        </BaseSelect>
      </div>

      {/* 테이블 */}
      <div className="w-full">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map(headerGroup => (
              <TableRow key={headerGroup.id} className="border-b-line">
                {headerGroup.headers.map(header => (
                  <TableHead
                    className="text-size-sm text-gray-3 w-1/3 font-semibold"
                    key={header.id}
                  >
                    {header.isPlaceholder
                      ? null
                      : flexRender(header.column.columnDef.header, header.getContext())}
                  </TableHead>
                ))}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map(row => (
                <TableRow key={row.id} className="hover:bg-gray-1 border-0">
                  {row.getVisibleCells().map(cell => (
                    <TableCell className="w-1/3" key={cell.id}>
                      {flexRender(cell.column.columnDef.cell, cell.getContext())}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={columns.length} className="py-10 text-center">
                  <div className="text-gray-600">No activities found</div>
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}
