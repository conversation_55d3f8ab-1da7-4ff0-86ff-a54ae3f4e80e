'use client';

import {
  BaseSelect,
  BaseSelectContent,
  BaseSelectItem,
  BaseSelectTrigger,
  BaseSelectValue,
} from '@/components/ui/base.select';
import TableMarketColumn from '@/components/ui/TableMarketColumn';
import { usePortfolioDisputes } from '@/hooks/query/portfolio';
import { DisputeOrder } from '@/lib/api/portfolio/portfolio.schema';
import { formatCurrency } from '@/lib/format';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@repo/ui/components/table';
import { cn } from '@repo/ui/lib/utils';
import { useState } from 'react';

const sortOptions = [
  { value: 'newest' as DisputeOrder, label: 'Newest' },
  { value: 'value' as DisputeOrder, label: 'Value' },
];

interface DisputeTabProps {
  className?: string;
}

export default function DisputeTab({ className }: DisputeTabProps) {
  const [sortBy, setSortBy] = useState<DisputeOrder>('newest');
  const [page, setPage] = useState(0);

  const {
    data: disputesData,
    isLoading,
    error,
  } = usePortfolioDisputes({
    page,
    limit: 20,
    order: sortBy,
  });

  if (isLoading && page === 0) {
    return (
      <div className={cn('mt-space-30', className)}>
        <div className="flex items-center justify-center py-20">
          <div className="text-gray-600">Loading disputes...</div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={cn('mt-space-30', className)}>
        <div className="flex items-center justify-center py-20">
          <div className="text-red-600">Failed to load disputes</div>
        </div>
      </div>
    );
  }

  const disputes = disputesData?.disputes || [];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'PENDING':
        return 'text-yellow-600';
      case 'ACCEPTED':
        return 'text-yes-green';
      case 'REJECTED':
        return 'text-no-red';
      default:
        return 'text-gray-600';
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'PENDING':
        return 'Pending';
      case 'ACCEPTED':
        return 'Accepted';
      case 'REJECTED':
        return 'Rejected';
      default:
        return status;
    }
  };

  return (
    <div className={cn('mt-space-30', className)}>
      {/* 필터 영역 */}
      <div className="mb-space-20 gap-space-10 flex">
        {/* 정렬 필터 */}
        <BaseSelect value={sortBy} onValueChange={value => setSortBy(value as DisputeOrder)}>
          <BaseSelectTrigger className="w-[150px]">
            <BaseSelectValue placeholder="Sort by" />
          </BaseSelectTrigger>
          <BaseSelectContent>
            {sortOptions.map(option => (
              <BaseSelectItem key={option.value} value={option.value}>
                {option.label}
              </BaseSelectItem>
            ))}
          </BaseSelectContent>
        </BaseSelect>
      </div>

      {/* 테이블 */}
      <div className="w-full">
        <Table>
          <TableHeader>
            <TableRow className="border-b-line">
              <TableHead className="text-size-sm text-gray-3 font-semibold">Market</TableHead>
              <TableHead className="text-size-sm text-gray-3 font-semibold">Amount</TableHead>
              <TableHead className="text-size-sm text-gray-3 font-semibold">Status</TableHead>
              <TableHead className="text-size-sm text-gray-3 font-semibold">Date</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {disputes.length > 0 ? (
              disputes.map(dispute => (
                <TableRow key={dispute.id} className="hover:bg-gray-1 border-0">
                  <TableCell>
                    <TableMarketColumn
                      title={dispute.marketTitle}
                      avatarUrl={dispute.marketImageUrl || ''}
                      outcome={{
                        label: 'Dispute',
                        order: 1,
                      }}
                    />
                  </TableCell>
                  <TableCell>
                    <div className="text-mid-dark text-size-sm font-medium">
                      {formatCurrency(dispute.disputeAmount)}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className={cn('text-size-sm font-medium', getStatusColor(dispute.status))}>
                      {getStatusLabel(dispute.status)}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="text-size-sm text-gray-600">
                      {new Date(dispute.createdAt).toLocaleDateString()}
                    </div>
                  </TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={4} className="py-10 text-center">
                  <div className="text-gray-600">No disputes found</div>
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}
