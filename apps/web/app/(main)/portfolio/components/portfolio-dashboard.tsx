'use client';

import { pxToRem } from '@repo/ui/lib/utils';
import { formatCurrency } from '@/lib/format';
import { useMyPortfolio } from '@/hooks/query/portfolio';
import { useState } from 'react';

type ProfitPeriod = 'day' | 'week' | 'month' | 'total';

export default function PortfolioDashboard() {
  const { data: portfolioData, isLoading, error } = useMyPortfolio();
  const [selectedPeriod, setSelectedPeriod] = useState<ProfitPeriod>('total');

  const periodButtons = [
    { key: 'day' as ProfitPeriod, label: '1D' },
    { key: 'week' as ProfitPeriod, label: '1W' },
    { key: 'month' as ProfitPeriod, label: '1M' },
    { key: 'total' as ProfitPeriod, label: 'All' },
  ];

  const getCurrentProfit = () => {
    if (!portfolioData) return 0;
    return Number(portfolioData.profit[selectedPeriod]);
  };

  if (isLoading) {
    return (
      <section
        style={
          {
            '--box-height': pxToRem(165),
          } as React.CSSProperties
        }
        className="gap-space-60 mb-space-20 flex"
      >
        <div className="px-space-20 py-space-30 border-line borer-line bg-gray-2 flex h-(--box-height) flex-1 flex-col justify-between border">
          <h3 className="dashboard-h3">Portfolio</h3>
          <strong className="text-size-2xl font-bold">Loading...</strong>
        </div>
        <div className="px-space-20 py-space-30 border-line borer-line bg-gray-2 flex h-(--box-height) flex-1 flex-col justify-between border">
          <h3 className="dashboard-h3">Profit / Loss</h3>
          <strong className="text-size-2xl font-bold">Loading...</strong>
        </div>
      </section>
    );
  }

  if (error) {
    return (
      <section
        style={
          {
            '--box-height': pxToRem(165),
          } as React.CSSProperties
        }
        className="gap-space-60 mb-space-20 flex"
      >
        <div className="px-space-20 py-space-30 border-line borer-line bg-gray-2 flex h-(--box-height) flex-1 flex-col justify-between border">
          <h3 className="dashboard-h3">Portfolio</h3>
          <strong className="text-size-2xl font-bold text-red-600">Error</strong>
        </div>
        <div className="px-space-20 py-space-30 border-line borer-line bg-gray-2 flex h-(--box-height) flex-1 flex-col justify-between border">
          <h3 className="dashboard-h3">Profit / Loss</h3>
          <strong className="text-size-2xl font-bold text-red-600">Error</strong>
        </div>
      </section>
    );
  }

  return (
    <section
      style={
        {
          '--box-height': pxToRem(165),
        } as React.CSSProperties
      }
      className="gap-space-60 mb-space-20 flex"
    >
      <div className="px-space-20 py-space-30 border-line borer-line bg-gray-2 flex h-(--box-height) flex-1 flex-col justify-between border">
        <h3 className="dashboard-h3">Portfolio</h3>
        <strong className="text-size-2xl font-bold">
          {portfolioData ? formatCurrency(portfolioData.positionsValue) : '$0'}
        </strong>
      </div>
      <div className="px-space-20 py-space-30 border-line borer-line bg-gray-2 flex h-(--box-height) flex-1 flex-col justify-between border">
        <div className="flex items-center justify-between">
          <h3 className="dashboard-h3">Profit / Loss</h3>
          <div className="gap-space-5 flex">
            {periodButtons.map(button => (
              <button
                key={button.key}
                onClick={() => setSelectedPeriod(button.key)}
                className={`px-space-10 py-space-5 text-size-xs border transition-colors ${
                  selectedPeriod === button.key
                    ? 'border-blue-500 bg-blue-500 text-white'
                    : 'border-gray-300 bg-white text-gray-600 hover:bg-gray-50'
                }`}
              >
                {button.label}
              </button>
            ))}
          </div>
        </div>
        <strong
          className={`text-size-2xl font-bold ${
            portfolioData && getCurrentProfit() >= 0 ? 'text-green-600' : 'text-red-600'
          }`}
        >
          {portfolioData
            ? `${getCurrentProfit() >= 0 ? '+' : ''}${formatCurrency(getCurrentProfit())}`
            : '$0'}
        </strong>
      </div>
    </section>
  );
}
