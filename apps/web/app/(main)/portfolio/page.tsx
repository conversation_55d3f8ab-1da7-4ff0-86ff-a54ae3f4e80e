'use client';

import { BaseButton } from '@/components/ui/base.button';
import { pxToRem } from '@repo/ui/lib/utils';
import {
  BaseTabs,
  BaseTabsList,
  BaseTabsTrigger,
  BaseTabsContent,
} from '@/components/ui/base.tabs';
import PortfolioPositionsTab from './components/portfolio-postions-tab';
import PortfolioActivityTab from './components/portfolio-activities-tab';
import DisputeTab from './components/DisputeTab';
import PortfolioDashboard from './components/portfolio-dashboard';
import { useClaimableDispute, useMyPortfolio } from '@/hooks/query/portfolio';
import { formatCurrency } from '@/lib/format';
import { useMemo } from 'react';

export default function PortfolioPage() {
  const { data: claimableData, isLoading: claimableLoading } = useClaimableDispute();
  const { data: portfolioData } = useMyPortfolio();

  const portfolioMetrics = useMemo(() => {
    return {
      claimableAmount: claimableData?.claimable || 0,
      portfolioValue: portfolioData?.positionsValue || 0,
    };
  }, [claimableData, portfolioData]);

  return (
    <div className="page">
      <h1 className="text-size-lg text-mid-dark mb-space-30 font-bold">Portfolio</h1>

      <PortfolioDashboard />

      {/* Funds Section */}
      <section className="gap-space-60 flex items-center">
        <div className="px-space-20 py-space-30 flex-1">
          <div className="text-size-sm font-semibold">Funds</div>
        </div>
        <div className="px-space-20 py-space-30 flex flex-1 items-center justify-between">
          <div className="pl-space-20 text-size-xl text-mid-dark font-bold">
            {portfolioMetrics ? formatCurrency(portfolioMetrics.portfolioValue) : '$0'}
          </div>
          <div className="gap-space-20 pr-space-20 flex items-center">
            <BaseButton className="px-space-60" variant="info">
              Deposit
            </BaseButton>
            <BaseButton className="px-space-60" variant="dark">
              Withdraw
            </BaseButton>
          </div>
        </div>
      </section>

      {/* Tabs Section */}
      <section>
        <BaseTabs defaultValue="positions">
          <div className="border-b-line border-b">
            <BaseTabsList className="border-none">
              <BaseTabsTrigger className="min-w-(--tab-trigger-width)" value="positions">
                Positions
              </BaseTabsTrigger>
              <BaseTabsTrigger className="min-w-(--tab-trigger-width)" value="activity">
                Activity
              </BaseTabsTrigger>
              <BaseTabsTrigger className="min-w-(--tab-trigger-width)" value="dispute">
                Dispute
              </BaseTabsTrigger>
            </BaseTabsList>
          </div>
          <BaseTabsContent value="positions">
            <PortfolioPositionsTab />
          </BaseTabsContent>
          <BaseTabsContent value="activity">
            <PortfolioActivityTab />
          </BaseTabsContent>
          <BaseTabsContent value="dispute">
            <div
              style={
                {
                  '--value-input-height': pxToRem(42),
                  '--base-button-width': pxToRem(140),
                } as React.CSSProperties
              }
              className="gap-space-30 pb-space-40 border-b-line flex flex-col border-b"
            >
              <h2 className="dashboard-h2">Deposits & Rewards</h2>
              <div className="gap-space-15 flex flex-col">
                <h3 className="dashboard-h3">Claimable</h3>
                <div className="gap-space-10 flex h-(--value-input-height)">
                  <div className="border-line px-space-20 bg-gray-2 flex h-full min-w-[510px] items-center justify-end border">
                    <span className="text-mid-dark text-size-lg font-bold">
                      {claimableLoading
                        ? 'Loading...'
                        : formatCurrency(portfolioMetrics?.claimableAmount || 0)}
                    </span>
                  </div>
                  <BaseButton
                    variant="info"
                    size="sm2"
                    className="h-full w-(--base-button-width)"
                    disabled={!portfolioMetrics?.claimableAmount || claimableLoading}
                  >
                    Claim
                  </BaseButton>
                </div>
              </div>
            </div>
            <DisputeTab />
          </BaseTabsContent>
        </BaseTabs>
      </section>
    </div>
  );
}
