'use client';
import { useParams, useRouter } from 'next/navigation';
import PostCreateView from '../../components/post-create-view';

export default function CreatePostPage() {
  const params = useParams();
  const router = useRouter();
  const channelId = params.channelId as string;

  const handleBack = () => {
    router.push(`/channels/${channelId}/posts`);
  };

  return <PostCreateView onBack={handleBack} channelId={channelId} />;
}
