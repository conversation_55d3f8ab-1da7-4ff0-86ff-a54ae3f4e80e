'use client';
import SvgIcon from '@/components/icons/svg-icon';
import CommonAvatar from '@/components/ui/avatar-image';
import { BaseButton } from '@/components/ui/base.button';
import { BaseTabs, BaseTabsList, BaseTabsTrigger } from '@/components/ui/base.tabs';
import { useChannelByIdWithUser } from '@/hooks/query/channel';
import { useToggleChannelSubscription } from '@/hooks/query/channel/use-toggle-channel-subscription';
import { useGlobalStore } from '@/store/global.store';
import { pxToRem } from '@repo/ui/lib/utils';
import Link from 'next/link';
import { useParams, usePathname } from 'next/navigation';
import ChannelBanner from './components/channel-banner';
import { Switch } from '@repo/ui/components/switch';
import { useEffect, useState } from 'react';

export default function ChannelLayout({ children }: { children: React.ReactNode }) {
  const params = useParams();
  const pathname = usePathname();
  const channelId = params.channelId as string;
  const { data, isLoading, error, refetch } = useChannelByIdWithUser(channelId);
  const { safeSmartAccount } = useGlobalStore();
  const toggleSubscription = useToggleChannelSubscription();
  const [optimisticSubscribed, setOptimisticSubscribed] = useState<boolean | undefined>(
    data?.channelIsSubscribed
  );

  useEffect(() => {
    setOptimisticSubscribed(data?.channelIsSubscribed);
  }, [data?.channelIsSubscribed]);

  const handleToggleSubscription = async () => {
    if (!channelId || typeof optimisticSubscribed !== 'boolean') return;
    const previousState = optimisticSubscribed;
    setOptimisticSubscribed(!optimisticSubscribed);

    try {
      await toggleSubscription.mutateAsync({
        channelId: channelId,
        isCurrentlySubscribed: optimisticSubscribed,
      });

      // 성공 시 서버 데이터 다시 가져오기
      await refetch();
    } catch (error) {
      // 실패 시 이전 상태로 롤백
      setOptimisticSubscribed(previousState);
      console.error('Failed to toggle subscription:', error);
    }
  };

  const getActiveTab = () => {
    if (pathname.includes('/leaderboard')) return 'leaderboard';
    if (pathname.includes('/posts')) return 'posts';
    return '';
  };

  const isOwnChannel = safeSmartAccount?.address.toLowerCase() === channelId.toLowerCase();
  if (isLoading) {
    return (
      <div className="page p-4">
        <div className="flex min-h-[400px] items-center justify-center">
          <div className="text-lg">Loading channel...</div>
        </div>
      </div>
    );
  }

  if (error || !data) {
    return (
      <div className="page p-4">
        <div className="flex min-h-[400px] items-center justify-center">
          <div className="text-lg text-red-500">
            {error ? 'Failed to load channel' : 'Channel not found'}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="page p-4">
      <section className="mb-8">
        <div className="mb-6">
          <ChannelBanner imageUrl={data.channelBannerUrl} />
        </div>
        <div className="mb-6 flex items-start gap-6">
          <div className="shrink-0">
            <CommonAvatar
              imageUrl={data.channelAvatarUrl}
              size="lg"
              alt={data.channelName}
              className="h-20 w-20"
            />
          </div>
          <div className="flex-1">
            <h1 className="text-dark-deep mb-2 text-2xl font-bold">{data.channelName}</h1>

            <div className="mb-3 flex items-center gap-4">
              <Link href={`/${channelId}/positions`} className="flex items-center">
                <SvgIcon name="CrownIcon" className="size-[14px]" />
                <span className="text-size-xs text-gray-3">{data.userNickname}</span>
              </Link>
              <div className="flex items-center text-sm text-gray-500">
                <SvgIcon name="MemberIcon" className="size-[14px]" />
                <span className="text-size-xs text-mid-dark font-semibold">
                  {data.channelIsLeaderLive ? 'Online' : 'Offline'}
                </span>
              </div>
            </div>

            <div className="gap-space-20 text-size-xs flex">
              <div className="gap-space-5 flex items-center">
                <SvgIcon name="SubscriptionInversedIcon" />
                <span className="text-gray-3">Subscribers</span>
                <span className="text-icon-dark">{data.channelSubscribers.toLocaleString()}</span>
              </div>
              <div className="gap-space-5 flex items-center">
                <SvgIcon name="DollarIcon" />
                <span className="text-gray-3">Total Volume: </span>
                <span className="text-icon-dark">
                  ${Number(data.channelTotalVolume).toLocaleString()}
                </span>
              </div>
              <div className="gap-space-5 flex items-center">
                <SvgIcon name="MarketInversedIcon" />
                <span className="text-gray-3">Total Markets: </span>
                <span className="text-icon-dark">{data.channelTotalMarkets}</span>
              </div>
              <div className="gap-space-5 flex items-center">
                <SvgIcon name="SubscriptionInversedIcon" />
                <span className="text-gray-3">Subscribers: </span>
                <span className="text-icon-dark">{data.channelSubscribers.toLocaleString()}</span>
              </div>
              {!isOwnChannel && safeSmartAccount && (
                <div className="gap-space-5 flex items-center">
                  <Switch
                    id={`subscribe-${channelId}`}
                    checked={optimisticSubscribed || false}
                    onCheckedChange={handleToggleSubscription}
                    disabled={toggleSubscription.isPending}
                    className="data-[state=checked]:bg-sky data-[state=unchecked]:bg-gray-3"
                  />
                  <label htmlFor={`subscribe-${channelId}`} className="text-size-xs">
                    {optimisticSubscribed ? 'Subscribed' : 'Subscribe'}
                  </label>
                </div>
              )}
            </div>
            {data.channelDescription && (
              <div className="text-size-sm text-gray-3 mt-space-20">{data.channelDescription}</div>
            )}
          </div>

          <div className="gap-space-10 flex">
            {data.channelSns.map((social, index) => (
              <a
                key={`${social.snsType}-${index}`}
                href={social.snsUrl || '#'}
                target="_blank"
                rel="noopener noreferrer"
                className="rounded-full transition-colors hover:bg-gray-100"
                aria-label={social.snsType}
              >
                <SvgIcon name="DiscordIcon" className="size-[24px]" />
              </a>
            ))}
          </div>
        </div>
        {isOwnChannel && (
          <div className="flex justify-end">
            <Link href={`/create-prediction`} className="flex items-center">
              <BaseButton variant="info" size="lg" className="w-[256px] px-8">
                <SvgIcon data-label="icon" name="MarketIcon" className="size-[24px] text-white" />
                Create Prediction
              </BaseButton>
            </Link>
          </div>
        )}
      </section>

      <section
        style={
          {
            '--tab-trigger-width': pxToRem(200),
          } as React.CSSProperties
        }
      >
        <BaseTabs defaultValue="markets">
          <div className="border-b-line border-b">
            <BaseTabsList className="border-none">
              <Link href={`/channels/${channelId}/markets`}>
                <BaseTabsTrigger
                  className="min-w-(--tab-trigger-width)"
                  value="markets"
                  data-state={getActiveTab() === '' ? 'active' : 'inactive'}
                >
                  Markets
                </BaseTabsTrigger>
              </Link>
              <Link href={`/channels/${channelId}/leaderboard`}>
                <BaseTabsTrigger
                  className="min-w-(--tab-trigger-width)"
                  value="leaderboard"
                  data-state={getActiveTab() === 'leaderboard' ? 'active' : 'inactive'}
                >
                  Leaderboard
                </BaseTabsTrigger>
              </Link>
              <Link href={`/channels/${channelId}/posts`}>
                <BaseTabsTrigger
                  className="min-w-(--tab-trigger-width)"
                  value="posts"
                  data-state={getActiveTab() === 'posts' ? 'active' : 'inactive'}
                >
                  Posts
                </BaseTabsTrigger>
              </Link>
            </BaseTabsList>
          </div>
          {children}
        </BaseTabs>
      </section>
    </div>
  );
}
