'use client';

import { useState, useMemo } from 'react';
import { cn } from '@repo/ui/lib/utils';
import SvgIcon from '@/components/icons/svg-icon';
import { shortenAddress } from '@repo/ui/lib/utils';
import Link from 'next/link';
import CommonAvatar from '@/components/ui/avatar-image';
import {
  useChannelLeaderboardByProfit,
  ChannelLeaderboardProps,
} from '@/hooks/query/channel/use-channel-leaderboard';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@repo/ui/components/table';
import {
  ColumnDef,
  flexRender,
  getCoreRowModel,
  getSortedRowModel,
  SortingState,
  useReactTable,
} from '@tanstack/react-table';

// 리더보드 항목 타입
type LeaderboardItem = ChannelLeaderboardProps['rankings'][number];

// 금액 포맷팅 함수
const formatAmount = (amount: number): string => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(amount);
};

interface ChannelLeaderboardTabProps {
  channelId: string;
}

export default function ChannelLeaderboardTab({ channelId }: ChannelLeaderboardTabProps) {
  const { data: leaderboardData, isLoading, error } = useChannelLeaderboardByProfit(channelId);

  const [sorting, setSorting] = useState<SortingState>([
    {
      id: 'userPnl',
      desc: true,
    },
  ]);

  // 랭크 표시 셀 렌더링 함수
  const renderRankCell = (userRank: number) => {
    if (userRank === 1) return <SvgIcon name="GoldTrophyIcon" className="h-5 w-5" />;
    if (userRank === 2) return <SvgIcon name="SilverTrophyIcon" className="h-5 w-5" />;
    if (userRank === 3) return <SvgIcon name="BronzeTrophyIcon" className="h-5 w-5" />;
    return <span className="text-dark-deep text-size-sm">{userRank}</span>;
  };

  const columns = useMemo<ColumnDef<LeaderboardItem>[]>(
    () => [
      {
        accessorKey: 'userRank',
        header: () => <div className="text-center">Rank</div>,
        id: 'Rank',
        size: 60,
        cell: ({ row }) => {
          return <div className="flex justify-center">{renderRankCell(row.original.userRank)}</div>;
        },
      },
      {
        accessorKey: 'userNickname',
        header: () => <div className="text-center">Predictor</div>,
        id: 'Predictor',
        cell: ({ row }) => {
          const item = row.original;
          return (
            <div className="flex items-center justify-center gap-3">
              <CommonAvatar imageUrl={item.userAvatarurl} size="sm" alt={`User ${item.userRank}`} />
              <Link href={`/${item.userAddress}/positions`} className="text-size-sm text-mid-dark">
                {item.userNickname}
              </Link>
            </div>
          );
        },
      },
      {
        accessorKey: 'userPnl',
        header: ({ column }) => {
          return (
            <div
              className="flex cursor-pointer items-center justify-center gap-1"
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              PNL
              <SvgIcon
                name="ChevronNextIcon"
                className={cn(
                  'h-4 w-4 rotate-90 transition-transform',
                  column.getIsSorted() === 'asc' && 'rotate-[270deg]'
                )}
              />
            </div>
          );
        },
        cell: ({ row }) => {
          const pnl = row.original.userPnl;
          return <div className="text-center font-medium">{formatAmount(Number(pnl))}</div>;
        },
      },
      {
        accessorKey: 'userVolume',
        header: ({ column }) => {
          return (
            <div
              className="flex cursor-pointer items-center justify-center gap-1"
              onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
            >
              Volume
              <SvgIcon
                name="ChevronNextIcon"
                className={cn(
                  'h-4 w-4 rotate-90 transition-transform',
                  column.getIsSorted() === 'asc' && 'rotate-[270deg]'
                )}
              />
            </div>
          );
        },
        cell: ({ row }) => {
          return (
            <div className="text-center font-medium">
              {formatAmount(Number(row.original.userVolume))}
            </div>
          );
        },
      },
    ],
    []
  );

  // 테이블 인스턴스 생성
  const table = useReactTable({
    data: leaderboardData?.rankings || [],
    columns,
    state: {
      sorting,
    },
    onSortingChange: setSorting,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
  });

  if (isLoading) {
    return (
      <div className="flex h-64 items-center justify-center">
        <div className="text-lg">Loading leaderboard...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex h-64 items-center justify-center">
        <div className="text-lg text-red-500">Failed to load leaderboard</div>
      </div>
    );
  }

  return (
    <div className="w-full">
      <Table>
        <TableHeader>
          {table.getHeaderGroups().map(headerGroup => (
            <TableRow key={headerGroup.id} className="border-b-line">
              {headerGroup.headers.map(header => (
                <TableHead
                  key={header.id}
                  className="text-size-xs text-gray-3 py-space-20"
                  style={{ width: header.id === 'Rank' ? '10%' : '30%' }}
                >
                  {header.isPlaceholder
                    ? null
                    : flexRender(header.column.columnDef.header, header.getContext())}
                </TableHead>
              ))}
            </TableRow>
          ))}
        </TableHeader>
        <TableBody>
          {table.getRowModel().rows?.length ? (
            table.getRowModel().rows.map(row => (
              <TableRow
                key={row.id}
                className="hover:bg-gray-1 py-space-15 border-0 transition-colors"
              >
                {row.getVisibleCells().map(cell => (
                  <TableCell
                    key={cell.id}
                    style={{ width: cell.column.id === 'Rank' ? '10%' : '30%' }}
                  >
                    {flexRender(cell.column.columnDef.cell, cell.getContext())}
                  </TableCell>
                ))}
              </TableRow>
            ))
          ) : (
            <TableRow>
              <TableCell colSpan={columns.length} className="h-24 text-center">
                No results.
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>
    </div>
  );
}
