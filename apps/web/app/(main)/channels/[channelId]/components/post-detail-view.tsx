import CommentInput from '@/components/actions/comment-input';
import CommentReplies from '@/components/common/comment-replies';
import SvgIcon from '@/components/icons/svg-icon';
import CommonAvatar from '@/components/ui/avatar-image';
import { BaseButton } from '@/components/ui/base.button';
import {
  BaseDropdownMenu,
  BaseDropdownMenuContent,
  BaseDropdownMenuItem,
  BaseDropdownMenuTrigger,
} from '@/components/ui/base.dropdown-menu';
import { toast } from '@/components/ui/base.toast';
import { Popup } from '@/components/ui/popup';
import DeleteConfirmationPopup from '@/components/ui/popup/delete-confirmation-popup';
import { useDeletePost, useComments, useCreateComment } from '@/hooks/query/board';
import Link from 'next/link';
import { useState } from 'react';
import { PostType } from './post-adapter';

interface PostDetailViewProps {
  post: PostType;
  onBack: () => void;
}

export default function PostDetailView({ post, onBack }: PostDetailViewProps) {
  const [replyingToCommentId, setReplyingToCommentId] = useState<string | null>(null);
  const [showRepliesForComment, setShowRepliesForComment] = useState<Record<string, boolean>>({});
  const [showDeleteConfirmation, setShowDeleteConfirmation] = useState(false);

  // Fetch comments using the useComments hook
  const { data: commentsData, isLoading: commentsLoading } = useComments(post.id, {
    page: 0,
    limit: 50,
    order: 'latest',
  });

  // Mutations
  const deletePostMutation = useDeletePost();
  const createCommentMutation = useCreateComment();

  const handleReplyClick = (commentId: string) => {
    setReplyingToCommentId(commentId === replyingToCommentId ? null : commentId);
  };

  const handleReplySubmit = async (text: string) => {
    if (!replyingToCommentId) return;

    try {
      await createCommentMutation.mutateAsync({
        parentId: replyingToCommentId,
        data: { content: text },
      });
      toast.success('Reply submitted successfully');
      setReplyingToCommentId(null);
    } catch (error) {
      console.error('Failed to submit reply:', error);
      toast.error('Failed to submit reply. Please try again.');
    }
  };

  const handleReplyCancel = () => {
    setReplyingToCommentId(null);
  };

  const handleDeleteClick = () => {
    setShowDeleteConfirmation(true);
  };

  const handleDeleteConfirm = async () => {
    try {
      await deletePostMutation.mutateAsync(post.id);
      toast.success('Deleted successfully.');
      setShowDeleteConfirmation(false);
      onBack(); // Navigate back to list after successful deletion
    } catch (error) {
      console.error('Failed to delete post:', error);
      toast.error('Failed to delete post. Please try again.');
    }
  };

  const handleDeleteCancel = () => {
    setShowDeleteConfirmation(false);
  };

  const toggleRepliesVisibility = (commentId: string) => {
    setShowRepliesForComment(prev => ({
      ...prev,
      [commentId]: !prev[commentId],
    }));
  };

  const handleCommentSubmit = async (text: string) => {
    try {
      await createCommentMutation.mutateAsync({
        parentId: post.id,
        data: { content: text },
      });
      toast.success('Comment submitted successfully');
    } catch (error) {
      console.error('Failed to submit comment:', error);
      toast.error('Failed to submit comment. Please try again.');
    }
  };

  const formatTimestamp = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));

    if (diffInMinutes < 60) {
      return `${diffInMinutes}m ago`;
    } else if (diffInMinutes < 1440) {
      return `${Math.floor(diffInMinutes / 60)}h ago`;
    } else {
      return `${Math.floor(diffInMinutes / 1440)}d ago`;
    }
  };

  const comments = commentsData?.comments || [];
  const totalComments = commentsData?.totalLength || 0;

  return (
    <div className="flex flex-col">
      {/* Back button */}
      <div className="mb-space-20">
        <BaseButton
          variant="ghost"
          size="sm"
          className="gap-space-6 text-gray-3 hover:text-mid-dark flex items-center"
          onClick={onBack}
        >
          <SvgIcon name="ChevronNextIcon" className="h-4 w-4 rotate-180" />
          <span>Back to list</span>
        </BaseButton>
      </div>

      {/* Post details */}
      <div className="border-b-line pb-space-30 border-b">
        {/* Post header */}
        <div className="gap-space-15 mb-space-15 flex items-center">
          <CommonAvatar imageUrl={post.avatarUrl} size="md" alt={post.username} />
          <div className="flex-1">
            <Link
              href={`/profile/${post.username}`}
              className="text-mid-dark font-semibold hover:underline"
            >
              {post.username}
            </Link>
            <div className="text-size-xs text-gray-3">{post.timestamp}</div>
          </div>
          {post.isAuthor && (
            <div className="flex gap-2">
              <BaseDropdownMenu>
                <BaseDropdownMenuTrigger asChild>
                  <button className="text-gray-3 hover:text-mid-dark">
                    <SvgIcon name="ThreeDotsIcon" className="h-5 w-5" />
                  </button>
                </BaseDropdownMenuTrigger>
                <BaseDropdownMenuContent>
                  <BaseDropdownMenuItem className="gap-space-6 cursor-pointer py-2 text-sm">
                    <SvgIcon name="ShareBonusIcon" className="h-4 w-4" />
                    <span>Share</span>
                  </BaseDropdownMenuItem>
                  <BaseDropdownMenuItem className="gap-space-6 cursor-pointer py-2 text-sm">
                    <SvgIcon name="EditIcon" className="h-4 w-4" />
                    <span>Edit</span>
                  </BaseDropdownMenuItem>
                  <BaseDropdownMenuItem className="gap-space-6 cursor-pointer py-2 text-sm">
                    <SvgIcon name="ReportIcon" className="h-4 w-4" />
                    <span>Report</span>
                  </BaseDropdownMenuItem>
                  <BaseDropdownMenuItem
                    className="gap-space-6 cursor-pointer py-2 text-sm"
                    onClick={handleDeleteClick}
                  >
                    <SvgIcon name="DeleteIcon" className="h-4 w-4" />
                    <span>Delete</span>
                  </BaseDropdownMenuItem>
                </BaseDropdownMenuContent>
              </BaseDropdownMenu>
            </div>
          )}
        </div>

        {/* Post title (if exists) */}
        {post.title && (
          <div className="mb-space-10">
            <h2 className="text-mid-dark text-xl font-semibold">{post.title}</h2>
          </div>
        )}

        {/* Post content */}
        <div className="mb-space-15">
          <p className="text-mid-dark whitespace-pre-wrap">{post.content}</p>
        </div>

        {/* Post interactions */}
        <div className="text-size-sm text-gray-3 flex items-center justify-between">
          <div className="gap-space-15 flex">
            <button className="gap-space-6 hover:text-mid-dark flex items-center">
              <SvgIcon name="HeartIcon" className="h-4 w-4" />
              <span>{post.likes}</span>
            </button>
            <button className="gap-space-6 hover:text-mid-dark flex items-center">
              <SvgIcon name="ViewsIcon" className="h-4 w-4" />
              <span>{post.viewCount.toLocaleString()}</span>
            </button>
            <button className="gap-space-6 hover:text-mid-dark flex items-center">
              <SvgIcon name="ClockOutlineIcon" className="h-4 w-4" />
              <span>{post.timestamp}</span>
            </button>
          </div>
        </div>
      </div>

      {/* Comments section */}
      <div className="mt-space-30">
        <h3 className="mb-space-15 text-mid-dark font-semibold">Comments ({totalComments})</h3>

        {/* Comment input */}
        <div className="mb-space-20">
          <CommentInput parentId={post.id} />
        </div>

        {/* Comments list */}
        <div className="gap-space-30 flex flex-col">
          {commentsLoading ? (
            <div className="text-gray-3 py-space-20 text-center">Loading comments...</div>
          ) : comments.length > 0 ? (
            comments.map((comment, i) => {
              const commentId = comment.id;
              const isReplying = replyingToCommentId === commentId;
              const showReplies = showRepliesForComment[commentId] || false;

              return (
                <div key={commentId} className="border-b-line pb-space-30 border-b">
                  <div className="gap-space-15 mb-space-15 flex items-center">
                    <CommonAvatar
                      size="sm"
                      imageUrl={comment.author.imageUrl || ''}
                      alt={comment.author.nickname}
                    />
                    <div className="flex-1">
                      <div className="text-mid-dark font-semibold">{comment.author.nickname}</div>
                      <div className="text-size-xs text-gray-3">
                        {formatTimestamp(comment.createdAt)}
                      </div>
                    </div>
                  </div>
                  <div className="mb-space-15">
                    <p className="text-mid-dark whitespace-pre-wrap">{comment.content}</p>
                  </div>
                  <div className="text-size-sm text-gray-3 flex items-center justify-between">
                    <div className="gap-space-15 flex">
                      <button className="gap-space-6 hover:text-mid-dark flex items-center">
                        <SvgIcon name="HeartIcon" className="h-4 w-4" />
                        <span>{comment.likes}</span>
                      </button>
                      <button
                        className="gap-space-6 hover:text-mid-dark flex items-center"
                        onClick={() => handleReplyClick(commentId)}
                      >
                        <SvgIcon name="ReplyIcon" className="h-4 w-4" />
                        <span>{isReplying ? 'Cancel' : 'Reply'}</span>
                      </button>
                    </div>

                    {comment.commentCount > 0 && (
                      <button
                        className="text-size-sm font-medium text-blue-500 hover:text-blue-600"
                        onClick={() => toggleRepliesVisibility(commentId)}
                      >
                        {showReplies
                          ? `Hide ${comment.commentCount} Replies`
                          : `Show ${comment.commentCount} Replies`}
                      </button>
                    )}
                  </div>

                  {isReplying && (
                    <div className="mt-space-15 ml-space-40">
                      <CommentInput parentId={commentId} />
                    </div>
                  )}

                  {/* Show replies */}
                  {showReplies && comment.commentCount > 0 && (
                    <CommentReplies commentId={commentId} />
                  )}
                </div>
              );
            })
          ) : (
            <div className="text-gray-3 py-space-20 text-center">
              No comments yet. Be the first to comment!
            </div>
          )}
        </div>
      </div>

      {/* Delete Confirmation Popup */}
      <Popup isOpen={showDeleteConfirmation} onClose={handleDeleteCancel} showCloseButton={false}>
        <DeleteConfirmationPopup onConfirm={handleDeleteConfirm} onCancel={handleDeleteCancel} />
      </Popup>
    </div>
  );
}
