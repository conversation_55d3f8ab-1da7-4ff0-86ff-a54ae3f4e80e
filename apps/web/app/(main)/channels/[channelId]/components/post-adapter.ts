import { PostsResponse } from '@/lib/api/board/board.schema';

/**
 * API 응답의 개별 post를 클라이언트 Post 타입으로 변환하는 어댑터
 */
export function adaptApiPostToClientPost(apiPost: PostsResponse['posts'][0]) {
  return {
    id: apiPost.id,
    username: apiPost.author.nickname,
    avatarUrl: apiPost.author.imageUrl || `https://i.pravatar.cc/150?u=${apiPost.author.address}`,
    timestamp: formatTimestamp(apiPost.createdAt),
    content: apiPost.content,
    likes: apiPost.likes,
    comments: apiPost.commentCount,
    shares: 0, // API에서 제공하지 않는 경우 기본값 설정
    viewCount: apiPost.views,
    isAuthor: false, // 현재 사용자 정보와 비교해야 하는데, 일단 기본값으로 설정
    title: apiPost.title,
    isPinned: apiPost.isPinned,
    createdAt: apiPost.createdAt, // 원본 날짜 (정렬용)
    refId: apiPost.refId.toLowerCase(),
  };
}

export type PostType = ReturnType<typeof adaptApiPostToClientPost>;

/**
 * API 응답의 PostsResDto를 클라이언트 Post 타입으로 변환하는 어댑터
 */
export function adaptApiPostsToClientPosts(apiResponse: PostsResponse): PostType[] {
  return apiResponse.posts.map(adaptApiPostToClientPost);
}

/**
 * ISO 날짜 문자열을 사용자 친화적인 상대 시간으로 변환
 */
function formatTimestamp(isoString: string): string {
  const date = new Date(isoString);
  const now = new Date();
  const diffInMs = now.getTime() - date.getTime();
  const diffInMinutes = Math.floor(diffInMs / (1000 * 60));
  const diffInHours = Math.floor(diffInMs / (1000 * 60 * 60));
  const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24));

  if (diffInMinutes < 1) {
    return 'Just now';
  } else if (diffInMinutes < 60) {
    return `${diffInMinutes} minute${diffInMinutes > 1 ? 's' : ''} ago`;
  } else if (diffInHours < 24) {
    return `${diffInHours} hour${diffInHours > 1 ? 's' : ''} ago`;
  } else {
    return `${diffInDays} day${diffInDays > 1 ? 's' : ''} ago`;
  }
}

/**
 * 클라이언트 Post를 API 생성 요청 형태로 변환하는 어댑터
 */
export function adaptClientPostToApiCreateRequest(
  title: string,
  content: string,
  channelId: string
) {
  return {
    title,
    content,
    channelId,
  };
}
