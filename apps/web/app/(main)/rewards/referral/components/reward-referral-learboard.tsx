import SvgIcon from '@/components/icons/svg-icon';
import { BaseButton } from '@/components/ui/base.button';
import { useReferralLeaderboard } from '@/hooks/query/referral/use-referral-leaderboard';
import { cn } from '@repo/ui/lib/utils';
import Link from 'next/link';

interface RewardReferralLeaderboardProps {
  className?: string;
}

export default function RewardReferralLeaderboard({ className }: RewardReferralLeaderboardProps) {
  const { data: leaderboardData, isLoading } = useReferralLeaderboard();

  if (isLoading) {
    return <div>Loading leaderboard...</div>; // Or a skeleton loader
  }

  const showLeaderboard = leaderboardData && leaderboardData.length > 0;
  return (
    <section className="py-space-30">
      <h2 className="dashboard-h2 mb-space-20">Leaderboard</h2>
      <div className={cn('w-full', className)}>
        {/* 테이블 헤더 */}
        <div className="text-size-xs text-gray-3 border-b-line py-space-20 grid grid-cols-4 border-b">
          <div>Rank</div>
          <div>Inviter</div>
          <div className="text-center">Invitees</div>
          <div className="text-right">Referral Income</div>
        </div>

        <div className="divide-line divide-y">
          {showLeaderboard ? (
            leaderboardData.map((item, index) => {
              const rank = index + 1;

              const rankDisplay =
                rank === 1 ? (
                  <SvgIcon name="GoldTrophyIcon" />
                ) : rank === 2 ? (
                  <SvgIcon name="SilverTrophyIcon" />
                ) : rank === 3 ? (
                  <SvgIcon name="BronzeTrophyIcon" />
                ) : (
                  <span className="text-dark-deep text-size-xxs">{rank}</span>
                );

              return (
                <div
                  key={item.inviter}
                  className="hover:bg-gray-1 py-space-15 grid grid-cols-4 items-center transition-colors"
                >
                  <div className="w-8 text-center">{rankDisplay}</div>
                  <div className="text-size-sm text-mid-dark font-semibold">
                    <Link href={`/channels/${item.address}/positions`}>{item.inviter}</Link>
                  </div>
                  <div className="text-size-sm text-mid-dark text-center font-semibold">
                    {item.invitees}
                  </div>
                  <div className="text-size-sm text-icon-dark text-right font-semibold">
                    ${item.referralIncome}
                  </div>
                </div>
              );
            })
          ) : (
            <section className="py-space-30 flex h-full flex-col items-center justify-center">
              <svg
                width="50"
                height="50"
                viewBox="0 0 50 50"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M0 25C0 11.1929 11.1929 0 25 0C38.8071 0 50 11.1929 50 25C50 38.8071 38.8071 50 25 50C11.1929 50 0 38.8071 0 25Z"
                  fill="#5AC8FA"
                />
                <path
                  fill-rule="evenodd"
                  clip-rule="evenodd"
                  d="M29.5346 14.389C29.8722 13.7868 30.634 13.5723 31.2362 13.9098C33.0264 14.9133 34.375 16.6377 34.375 18.7502C34.375 20.8628 33.0264 22.5871 31.2362 23.5906C30.634 23.9282 29.8722 23.7136 29.5346 23.1114C29.1971 22.5092 29.4116 21.7474 30.0138 21.4098C31.2574 20.7127 31.875 19.7108 31.875 18.7502C31.875 17.7896 31.2574 16.7877 30.0138 16.0906C29.4116 15.7531 29.1971 14.9912 29.5346 14.389ZM23.75 16.2502C22.3693 16.2502 21.25 17.3695 21.25 18.7502C21.25 20.1309 22.3693 21.2502 23.75 21.2502C25.1307 21.2502 26.25 20.1309 26.25 18.7502C26.25 17.3695 25.1307 16.2502 23.75 16.2502ZM18.75 18.7502C18.75 15.9888 20.9886 13.7502 23.75 13.7502C26.5114 13.7502 28.75 15.9888 28.75 18.7502C28.75 21.5116 26.5114 23.7502 23.75 23.7502C20.9886 23.7502 18.75 21.5116 18.75 18.7502ZM32.1875 24.6876C32.8779 24.6876 33.4375 25.2472 33.4375 25.9376V27.5002H35C35.6904 27.5002 36.25 28.0599 36.25 28.7502C36.25 29.4406 35.6904 30.0002 35 30.0002H33.4375V31.5626C33.4375 32.2529 32.8779 32.8126 32.1875 32.8126C31.4971 32.8126 30.9375 32.2529 30.9375 31.5626V30.0002H29.375C28.6846 30.0002 28.125 29.4406 28.125 28.7502C28.125 28.0599 28.6846 27.5002 29.375 27.5002H30.9375V25.9376C30.9375 25.2472 31.4971 24.6876 32.1875 24.6876ZM13.7504 31.2497C13.7507 28.4885 15.9892 26.2502 18.7504 26.2502H25.625C26.3154 26.2502 26.875 26.8099 26.875 27.5002C26.875 28.1906 26.3154 28.7502 25.625 28.7502H18.7504C17.3698 28.7502 16.2506 29.8693 16.2504 31.2499L16.25 35.0004C16.2499 35.6907 15.6902 36.2503 14.9999 36.2502C14.3095 36.2501 13.7499 35.6904 13.75 35.0001L13.7504 31.2497Z"
                  fill="white"
                />
              </svg>
              <p className="text-size-sm text-icon-dark">Invite your friends and earn rewards!</p>
              <Link href="/referral">
                <BaseButton size="lg" variant="info" className="px-space-30 py-space-15">
                  Invite Friends
                </BaseButton>
              </Link>
            </section>
          )}
        </div>
      </div>
    </section>
  );
}
