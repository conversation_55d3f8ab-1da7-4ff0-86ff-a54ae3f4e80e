import { useReferralDashboard } from '@/hooks/query/referral/use-referral-dashboard';
import { pxToRem } from '@repo/ui/lib/utils';
import { BaseButton } from '@/components/ui/base.button';
import React from 'react';

export function RewardReferralDashboard() {
  const { data, isLoading } = useReferralDashboard();

  if (isLoading || !data) {
    return <div>Loading dashboard...</div>; // Or a skeleton loader
  }

  const { totalCommissionReward, totalFeeRebate } = data;

  return (
    <section
      style={
        {
          '--box-height': pxToRem(165),
        } as React.CSSProperties
      }
      className="gap-space-30 py-space-50 border-b-line flex flex-col border-b"
    >
      <h2 className="dashboard-h2">Dashboard</h2>
      <div className="gap-space-60 flex">
        <div className="px-space-20 bg-gray-2 border-line py-space-30 flex h-(--box-height) flex-1 flex-col justify-between border">
          <h3 className="dashboard-h3">Total Referral Income</h3>
          <strong className="text-size-xl text-mid-dark font-bold">{totalCommissionReward}</strong>
        </div>
        <div className="px-space-20 py-space-30 border-line flex h-(--box-height) flex-1 flex-col justify-between border">
          <h3 className="dashboard-h3">Total Fee Rebate</h3>
          <strong className="text-size-xl text-mid-dark font-bold">{totalFeeRebate}</strong>
        </div>
      </div>
      <div className="gap-space-30 flex items-center justify-end">
        <p className="text-size-sm text-gray-3">
          Check out your rewards in the <span className="text-mid-dark">User Dashboard</span>
        </p>
        <BaseButton size="sm2" variant="info" className="px-space-20">
          Check Your Reward
        </BaseButton>
      </div>
    </section>
  );
}
