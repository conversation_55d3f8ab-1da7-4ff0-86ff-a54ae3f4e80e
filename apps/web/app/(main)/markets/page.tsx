'use client';

import { useState } from 'react';
import { MarketList } from '@/components/common/market-list';
import { pxToRem } from '@repo/ui/lib/utils';
import { GetMarketsOrderEnum, MarketStatus } from '@/lib/api/market/market.schema.server';
import MarketViewControl from './market-view-control';

export default function MarketsPage() {
  const [checkedValue, setCheckedValue] = useState<MarketStatus[]>([]);
  const [sortBy, setSortBy] = useState<GetMarketsOrderEnum>('VOLUME');

  const handleSortChange = (option: GetMarketsOrderEnum) => {
    setSortBy(option);
  };

  const handleStatusChange = (status: MarketStatus, checked: boolean) => {
    setCheckedValue(prev => {
      if (checked) {
        return [...prev, status];
      } else {
        return prev.filter(s => s !== status);
      }
    });
  };

  return (
    <div
      data-page="all-markets"
      style={
        {
          '--aside-width': pxToRem(340),
        } as React.CSSProperties
      }
      className="relative flex pr-(--aside-width)"
    >
      <section className="min-w-0 flex-1">
        <header className="p-space-30 pb-space-30">
          <h1 className="text-size-lg text-mid-dark font-bold">All Markets</h1>
        </header>
        <MarketList page={0} limit={50} order={sortBy} />
      </section>
      <aside className="border-l-line absolute top-0 right-0 bottom-0 z-10 w-(--aside-width) overflow-y-auto border-l bg-white">
        <MarketViewControl
          checkedValue={checkedValue}
          sortBy={sortBy}
          onSortChange={handleSortChange}
          onStatusChange={handleStatusChange}
        />
      </aside>
    </div>
  );
}
