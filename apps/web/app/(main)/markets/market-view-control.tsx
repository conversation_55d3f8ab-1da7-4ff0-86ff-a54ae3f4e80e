import { Checkbox } from '@repo/ui/components/checkbox';
import { cn } from '@repo/ui/lib/utils';
import { BaseButton } from '@/components/ui/base.button';
import SvgIcon from '@/components/icons/svg-icon';
import { GetMarketsOrderEnum, MarketStatus } from '@/lib/api/market/market.schema.server';

interface MarketViewControlProps {
  className?: string;
  checkedValue: MarketStatus[];
  sortBy: GetMarketsOrderEnum;
  onSortChange: (option: GetMarketsOrderEnum) => void;
  onStatusChange: (status: MarketStatus, checked: boolean) => void;
}

export default function MarketViewControl({
  className,
  checkedValue,
  sortBy,
  onSortChange,
  onStatusChange,
}: MarketViewControlProps) {
  return (
    <div className={cn('p-space-30 gap-space-30 flex flex-col', className)}>
      <div className="border-line pb-space-30 border-b">
        <h2 className="mb-space-20 text-lg font-semibold">Status</h2>
        <div className="space-y-space-15">
          <div className="flex items-center justify-between">
            <label
              htmlFor="bet-live"
              className="text-sm leading-none font-medium peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
            >
              Bet Live
            </label>
            <Checkbox
              id="bet-live"
              checked={checkedValue.includes(MarketStatus.OPEN)}
              onCheckedChange={checked => onStatusChange(MarketStatus.OPEN, checked as boolean)}
              className="data-[state=checked]:bg-sky data-[state=checked]:border-sky rounded-full"
            />
          </div>
          <div className="flex items-center justify-between">
            <label
              htmlFor="disputable"
              className="text-sm leading-none font-medium peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
            >
              Disputable
            </label>
            <Checkbox
              id="disputable"
              checked={checkedValue.includes(MarketStatus.DISPUTABLE)}
              onCheckedChange={checked =>
                onStatusChange(MarketStatus.DISPUTABLE, checked as boolean)
              }
              className="data-[state=checked]:bg-sky data-[state=checked]:border-sky rounded-full"
            />
          </div>
        </div>
      </div>

      <div>
        <h2 className="mb-space-20 text-lg font-semibold">Sort by</h2>
        <div className="gap-space-10 grid grid-cols-2">
          <SortButton active={sortBy === 'VOLUME'} onClick={() => onSortChange('VOLUME')}>
            <SvgIcon name="SortVolumeIcon" />
            Volume
          </SortButton>
          <SortButton active={sortBy === 'NEWEST'} onClick={() => onSortChange('NEWEST')}>
            <SvgIcon name="SortNewestIcon" />
            Newest
          </SortButton>
          <SortButton active={sortBy === 'ENDING_SOON'} onClick={() => onSortChange('ENDING_SOON')}>
            <SvgIcon name="SortEndingSoonIcon" />
            Ending soon
          </SortButton>
          <SortButton active={sortBy === 'COMPETITIVE'} onClick={() => onSortChange('COMPETITIVE')}>
            <SvgIcon name="SortCompetitiveIcon" />
            Competitive
          </SortButton>
        </div>
      </div>
    </div>
  );
}

interface SortButtonProps {
  children: React.ReactNode;
  active: boolean;
  onClick: () => void;
}

function SortButton({ children, active, onClick }: SortButtonProps) {
  return (
    <BaseButton
      variant="neutral"
      className={cn('text-mid-dark flex w-full items-center justify-start text-sm', {
        'border-sky': active,
      })}
      onClick={onClick}
    >
      {children}
    </BaseButton>
  );
}
