import DisputeButton from '@/components/actions/dispute-button';
import MarketCountdown from '@/components/common/market-countdown';
import { XlIcon } from '@/components/icons/xl-icon';

interface DisputeControlPanelProps {
  marketId: string;
  proposedOutcome: string;
  disputeEndTime: Date;
  disputeAmount: number;
  maxDeposit: number;
}

export default function DisputeControlPanel({
  marketId,
  proposedOutcome,
  disputeEndTime = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // Default 7 days from now
  disputeAmount,
  maxDeposit,
}: DisputeControlPanelProps) {
  return (
    <div className="flex flex-col">
      <h1 className="pb-space-30 dashboard-h2">Prediction Closed</h1>

      {/* Outcome proposed section */}
      <div className="mb-space-30 flex flex-col items-center">
        <div className="mb-space-15 gap-space-30 flex flex-col items-center">
          <XlIcon name="check" size={50} className="mr-space-10" />
          <div className="text-size-sm font-semibold">Outcome proposed</div>
        </div>
        <div className="p-space-15 flex items-center justify-between">
          <div className="text-size-sm font-semibold">{proposedOutcome}</div>
        </div>
      </div>

      {/* Dispute section */}
      <div className="mb-space-30 flex flex-col">
        <p className="mb-space-15 text-size-sm text-gray-3">
          Is it different from the proposed outcome?
        </p>

        {/* Progress bar */}
        <div className="mb-space-6 bg-gray-1 relative h-2 rounded-full">
          <div
            className="bg-sky absolute top-0 left-0 h-full rounded-full"
            style={{ width: '50%' }}
          ></div>
        </div>

        <div className="mb-space-15 flex justify-between">
          <span className="text-size-xs text-sky font-semibold">
            Dispute Confirmed {disputeAmount}
          </span>
        </div>
      </div>

      {/* Open Dispute Button */}
      <div className="mb-space-15">
        <DisputeButton
          marketId={marketId}
          size="xl2"
          className="w-full font-bold"
          textSize="base"
          maxDeposit={maxDeposit}
          disputeAmount={disputeAmount}
        />
      </div>

      {/* Time Remaining */}
      <div className="mb-space-15 gap-space-8 flex items-center justify-end">
        <MarketCountdown endTime={disputeEndTime} />
      </div>
    </div>
  );
}
