import { BaseButton } from '@/components/ui/base.button';
import { pxToRem } from '@repo/ui/lib/utils';

interface MarketOutcomesProps {
  outcomes: {
    text: string;
    order: number;
    volume: string;
    percentage: string;
  }[];
  handleClickOutcome: ({
    text,
    volume,
    order,
  }: {
    text: string;
    volume: string;
    order: number;
  }) => void;
}

const COLORS = [
  '--color-graph-1',
  '--color-graph-2',
  '--color-graph-3',
  '--color-graph-4',
  '--color-graph-5',
  '--color-graph-6',
  '--color-graph-7',
  '--color-graph-8',
  '--color-graph-9',
  '--color-graph-10',
  '--color-graph-11',
];

export default function MarketOutcomes({
  outcomes,
  handleClickOutcome: handleClick,
}: MarketOutcomesProps) {
  return (
    <div className="gap-space-20 flex flex-col">
      {outcomes.map((option, index) => (
        <div key={option.text} className="gap-space-30 flex items-center">
          <div className="gap-space-10 flex flex-col">
            <span className="text-mid-dark text-size-sm w-[300px] font-semibold">
              {option.text}
            </span>
            <div className="text-size-xs flex">
              <span className="text-black">${option.volume}</span>
              &nbsp;
              <span className="text-gray-3">Vol.</span>
            </div>
          </div>

          <div className="relative h-2 w-full overflow-hidden rounded-sm bg-gray-100">
            <div
              className="absolute top-0 left-0 h-full rounded-sm"
              style={{ width: option.percentage, backgroundColor: `var(${COLORS[option.order]})` }}
            />
          </div>
          <div>
            <span className="text-size-sm text-icon-dark font-semibold">{option.percentage}</span>
          </div>

          <div>
            <BaseButton
              style={{
                fontSize: 'var(--text-size-sm)',
                width: pxToRem(160),
              }}
              onClick={() =>
                handleClick({ text: option.text, volume: option.volume, order: option.order })
              }
              variant="yes"
              className="px-space-6 rounded-round-sm font-semibold"
              size="lg"
            >
              Predict
            </BaseButton>
          </div>
        </div>
      ))}
    </div>
  );
}
