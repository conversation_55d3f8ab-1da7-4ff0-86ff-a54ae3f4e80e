import { cn } from '@repo/ui/lib/utils';
import { Check } from 'lucide-react';

interface MarketStatusStepProps {
  currentStatus: string;
}

const STEPS = [
  {
    status: 'predict', // Matched to potential currentStatus values
    label: 'Predict',
  },
  {
    status: 'review',
    label: 'Reviewing Prediction',
  },
  {
    status: 'proposed',
    label: 'Outcome Proposed',
  },
  {
    status: 'disputed',
    label: 'Disputed',
  },
  {
    status: 'closed',
    label: 'Prediction close',
  },
];

// Helper component for the step icon
const StepIcon = ({ completed }: { completed: boolean }) => {
  if (completed) {
    return (
      <div className="flex size-[16px] items-center justify-center rounded-full bg-(--color-point-3)">
        <Check className="size-[10px] text-(--color-white)" strokeWidth={2.5} />
      </div>
    );
  }
  return <div className="size-[16px] rounded-full bg-(--color-icon-gray)" />;
};

export default function MarketStatusStep({ currentStatus }: MarketStatusStepProps) {
  const currentIndex = STEPS.findIndex(s => s.status === currentStatus);

  return (
    <div className="flex flex-col">
      {STEPS.map((step, index) => {
        // A step is completed if its index is less than or equal to the currentIndex.
        // If currentStatus is not found, currentIndex is -1, so all steps are pending.
        const isCompleted = index <= currentIndex;
        const isLastStep = index === STEPS.length - 1;

        // Determine the color of the line connecting this step to the next.
        // Line is blue if this step AND the next step are completed.
        // Otherwise, it's grey.
        let lineIsBlue = false;
        if (isCompleted && !isLastStep) {
          // Check if the next step is also completed
          const nextStepIsCompleted = index + 1 <= currentIndex;
          if (nextStepIsCompleted) {
            lineIsBlue = true;
          }
        }
        return (
          <div key={step.status} className="flex items-start">
            {/* Icon and vertical line part */}
            <div className="mr-3 flex flex-col items-center">
              <StepIcon completed={isCompleted} />
              {!isLastStep && (
                <div
                  className={cn('w-0.5', lineIsBlue ? 'bg-sky' : 'bg-gray-3')}
                  style={{ height: '28px' }}
                />
              )}
            </div>

            {/* Text part: step number and label */}
            <div className="text-size-sm mt-[-1px] font-semibold">
              <span className={cn('font-semibold', isCompleted ? 'text-sky' : 'text-gray-3')}>
                {`0${index + 1}`}
              </span>
              <span className={cn('ml-1.5', isCompleted ? 'text-mid-dark' : 'text-gray-3')}>
                {step.label}
              </span>
            </div>
          </div>
        );
      })}
    </div>
  );
}
