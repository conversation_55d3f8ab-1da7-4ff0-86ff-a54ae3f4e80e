import { cn } from '@repo/ui/lib/utils';
import CommonAvatar from '@/components/ui/avatar-image';
import SvgIcon from '@/components/icons/svg-icon';
import Link from 'next/link';

interface MarketAuthorProps {
  channelName: string;
  avatarUrl?: string;
  className?: string;
  totalVolume?: string;
  totalPredictors?: number;
  currentPredictions: number;
}

const NEED_PREDICTS = 10;
export default function MarketAuthor({
  channelName,
  avatarUrl,
  className,
  totalVolume,
  totalPredictors,
  currentPredictions,
}: MarketAuthorProps) {
  const isNeedPredicts = currentPredictions < NEED_PREDICTS;
  return (
    <div className={cn('text-size-xs flex items-center justify-between', className)}>
      <div className="gap-space-30 flex items-center">
        {/* Created by */}
        <Link href="/123/positions">
          <div className="gap-space-10 flex items-center">
            <div className="text-size-xxs text-gray-3">Created by:</div>
            <div className="gap-space-6 border-line flex items-center rounded-full border">
              <CommonAvatar imageUrl={avatarUrl} size="sm" alt={channelName} />
              <span className="text-size-xxs text-icon-dark pr-space-12 font-semibold">
                {channelName}
              </span>
            </div>
          </div>
        </Link>

        {/* Total Volume */}
        <div className="gap-space-5 flex items-center">
          <SvgIcon name="DollarIcon" />
          <span className="text-size-xs text-gray-3">Total Volume:</span>
          <span className="font-semibold">${totalVolume}</span>
        </div>

        {/* Total Predictors */}
        <div className="gap-space-5 flex items-center">
          <SvgIcon name="MemberIcon" />
          <span className="text-size-xs text-gray-3">Total Predictors:</span>
          <span className="font-semibold">{totalPredictors}</span>
        </div>
      </div>

      {/* Current Predictions */}
      {isNeedPredicts && (
        <div className="gap-space-5 flex items-center">
          <SvgIcon name="BombIcon" />
          <span className="text-size-xs text-gray-3">Needs 10 predicts</span>
          <span className="text-no-red font-semibold">
            {currentPredictions}/{NEED_PREDICTS}
          </span>
          <SvgIcon name="RedAlertIcon" />
        </div>
      )}
    </div>
  );
}
