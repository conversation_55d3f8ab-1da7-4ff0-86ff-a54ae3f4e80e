import { useState } from 'react';
import { useParams } from 'next/navigation';
import Link from 'next/link';
import {
  BaseSelect,
  BaseSelectContent,
  BaseSelectItem,
  BaseSelectTrigger,
  BaseSelectValue,
} from '@/components/ui/base.select';
import CommonAvatar from '@/components/ui/avatar-image';
import { useMarketOutcomes, useMarketTopPredictors } from '@/hooks/query/market';

const formatAmount = (amount: number): string => {
  if (amount >= 1_000_000) {
    return `$${(amount / 1_000_000).toFixed(1)}M`;
  } else if (amount >= 1_000) {
    return `$${(amount / 1_000).toFixed(1)}K`;
  } else {
    return `$${amount.toFixed(0)}`;
  }
};

export default function TopPredictorsTab() {
  const params = useParams();
  const marketId = params.marketId as string;
  const [selectedOutcome, setSelectedOutcome] = useState<string>('all');
  const { outcomes, isLoading: isMarketLoading } = useMarketOutcomes(marketId);
  const outcomesData = outcomes || [];

  const outcomeOptions = [
    ...outcomesData.map((outcome: any, index: number) => ({
      id: outcome.outcome,
      label: outcome.outcome,
      colorName: `graph-${index + 1}`, // 색상 클래스를 위한 동적 할당
    })),
  ];

  // 선택된 outcome에 따라 예측자 데이터 가져오기
  const {
    data: predictorsData,
    isLoading: isPredictorsLoading,
    error: predictorsError,
  } = useMarketTopPredictors(
    marketId,
    selectedOutcome === 'all' ? outcomesData[0]?.outcome || '' : selectedOutcome,
    { page: 0, limit: 50 }
  );

  const predictors = predictorsData?.topPredictors || [];

  // 로딩 상태
  if (isMarketLoading) {
    return (
      <div className="py-space-40 flex flex-col items-center justify-center text-center">
        <p className="text-gray-3 text-lg font-semibold">Loading market data...</p>
      </div>
    );
  }

  // outcomes가 없는 경우
  if (!outcomesData.length) {
    return (
      <div className="py-space-40 flex flex-col items-center justify-center text-center">
        <p className="text-gray-3 text-lg font-semibold">No outcomes available</p>
        <p className="text-gray-3 text-base">Market outcomes are not configured yet.</p>
      </div>
    );
  }

  return (
    <div>
      {/* 선택지 필터 */}
      <div className="flex justify-start">
        <BaseSelect value={selectedOutcome} onValueChange={value => setSelectedOutcome(value)}>
          <BaseSelectTrigger className="w-[180px]">
            <BaseSelectValue placeholder="Filter by outcome" />
          </BaseSelectTrigger>
          <BaseSelectContent>
            {outcomeOptions.map(option => (
              <BaseSelectItem key={option.id} value={option.id}>
                {option.label}
              </BaseSelectItem>
            ))}
          </BaseSelectContent>
        </BaseSelect>
      </div>

      {/* 예측자 목록 */}
      {isPredictorsLoading ? (
        <div className="py-space-40 flex flex-col items-center justify-center text-center">
          <p className="text-gray-3 text-lg font-semibold">Loading predictors...</p>
        </div>
      ) : predictorsError ? (
        <div className="py-space-40 flex flex-col items-center justify-center text-center">
          <p className="text-lg font-semibold text-red-500">Failed to load predictors</p>
          <p className="text-gray-3 text-base">Please try again later.</p>
        </div>
      ) : predictors.length > 0 ? (
        <div className="gap-space-20 flex flex-col">
          <div className="text-size-xs text-gray-3 border-b-line py-space-30 flex justify-between border-b">
            <div>Predictors</div>
            <div>Amount</div>
          </div>

          {predictors.map((predictor: any, index: number) => (
            <div
              key={predictor.address}
              className="hover:bg-gray-1 rounded-round-sm flex items-center justify-between transition-colors"
            >
              <div className="gap-space-15 flex min-w-0 flex-1 items-center">
                <CommonAvatar
                  imageUrl={predictor.imageUrl || '/default-avatar.svg'}
                  size="md"
                  alt={predictor.nickname}
                />
                <Link
                  href={`/${predictor.address}`}
                  className="text-size-sm text-mid-dark truncate font-semibold hover:underline"
                >
                  {predictor.nickname}
                </Link>
              </div>
              <div className="text-icon-dark text-size-sm flex-shrink-0 font-semibold">
                {formatAmount(Number(predictor.amount))}
              </div>
            </div>
          ))}
        </div>
      ) : (
        // 빈 상태 처리
        <div className="py-space-40 flex flex-col items-center justify-center text-center">
          <p className="text-gray-3 text-lg font-semibold">No predictors yet</p>
          <p className="text-gray-3 text-base">
            {selectedOutcome === 'all'
              ? 'Be the first to predict!'
              : `No predictors for ${selectedOutcome} yet`}
          </p>
        </div>
      )}
    </div>
  );
}
