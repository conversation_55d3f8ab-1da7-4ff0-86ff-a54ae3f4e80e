import CommentInput from '@/components/actions/comment-input';
import SvgIcon from '@/components/icons/svg-icon';
import CommonAvatar from '@/components/ui/avatar-image';
import {
  BaseSelect,
  BaseSelectContent,
  BaseSelectItem,
  BaseSelectTrigger,
  BaseSelectValue,
} from '@/components/ui/base.select';
import { useComments, useCreateComment, useLikePost } from '@/hooks/query/board';
import { toRelativeTime } from '@/lib/utils';
import { Checkbox } from '@repo/ui/components/checkbox';
import { cn } from '@repo/ui/lib/utils';
import { useState } from 'react';
import PositionsDropdown from '../positions-dropdown';

type FilterOption = 'newest' | 'oldest' | 'most_liked';
const filterOptions = [
  { value: 'newest', label: 'Newest' },
  { value: 'oldest', label: 'Oldest' },
  { value: 'most_liked', label: 'Most Liked' },
];

interface CommentsTabProps {
  marketId: string;
  className?: string;
}

export default function MarketCommentsTab({ marketId, className }: CommentsTabProps) {
  const [filter, setFilter] = useState<FilterOption>('newest');
  const [showOnlyPredictors, setShowOnlyPredictors] = useState(false);
  const [replyToCommentId, setReplyToCommentId] = useState<string | null>(null);
  const [showReplyInput, setShowReplyInput] = useState(false);

  const {
    data: commentsData,
    isLoading,
    error,
  } = useComments(marketId, {
    page: 0,
    limit: 50,
    order: filter === 'newest' ? 'latest' : 'likes',
    onlyPredictors: showOnlyPredictors ? 'true' : 'false',
    withPositions: 'true',
  });

  const createCommentMutation = useCreateComment();
  const likePostMutation = useLikePost();

  // 댓글 데이터 처리
  const comments = commentsData?.comments || [];

  // 댓글 생성 핸들러
  const handleCreateComment = async (content: string) => {
    try {
      await createCommentMutation.mutateAsync({
        parentId: marketId,
        data: { content },
      });
    } catch (error) {
      console.error('Failed to create comment:', error);
    }
  };

  // 포스트 좋아요/취소 핸들러
  const handleLikePost = async (postId: string) => {
    try {
      await likePostMutation.mutateAsync(postId);
    } catch (error) {
      console.error('Failed to like post:', error);
    }
  };

  if (isLoading) {
    return (
      <div className={cn('space-y-space-30', className)}>
        <div className="py-space-40 text-center">
          <p className="text-gray-3">댓글을 불러오는 중...</p>
        </div>
      </div>
    );
  }

  // 에러 상태 처리
  if (error) {
    return (
      <div className={cn('space-y-space-30', className)}>
        <div className="py-space-40 text-center">
          <p className="text-red-500">댓글을 불러오는데 실패했습니다.</p>
        </div>
      </div>
    );
  }

  return (
    <div className={cn('space-y-space-30', className)}>
      <CommentInput parentId={marketId} />

      <div className="gap-space-10 flex items-center">
        {/* 댓글 필터 */}
        <BaseSelect value={filter} onValueChange={value => setFilter(value as FilterOption)}>
          <BaseSelectTrigger className="w-[150px]">
            <BaseSelectValue placeholder="Filter comments" />
          </BaseSelectTrigger>
          <BaseSelectContent>
            {filterOptions.map(option => (
              <BaseSelectItem key={option.value} value={option.value}>
                {option.label}
              </BaseSelectItem>
            ))}
          </BaseSelectContent>
        </BaseSelect>

        {/* 예측자 필터 체크박스 */}
        <div className="gap-space-5 flex items-center justify-between">
          <Checkbox
            id="predictors-only"
            checked={showOnlyPredictors}
            onCheckedChange={checked => setShowOnlyPredictors(checked as boolean)}
            className="data-[state=checked]:bg-sky data-[state=checked]:border-sky rounded-full"
          />
          <label
            htmlFor="predictors-only"
            className="mr-space-10 text-sm leading-none font-medium peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
          >
            Predictors
          </label>
        </div>
      </div>

      <div className="space-y-space-20">
        {comments.length === 0 ? (
          <div className="py-space-40 text-center">
            <p className="text-gray-3">No comments yet. Be the first to comment!</p>
          </div>
        ) : (
          comments.map(comment => (
            <div key={comment.id} className="space-y-space-15">
              <div className="gap-space-15 flex">
                <CommonAvatar
                  imageUrl={comment.author.imageUrl || '/default-avatar.svg'}
                  size="md"
                  alt={comment.author.nickname}
                />
                <div className="flex-1">
                  <div className="gap-space-10 mb-space-6 flex items-center">
                    <span className="font-semibold">{comment.author.nickname}</span>
                    {comment.positions && comment.positions.length > 0 && (
                      <PositionsDropdown
                        positions={comment.positions.map(pos => ({
                          outcome: pos.outcome,
                          value: `$${pos.value}`,
                        }))}
                      />
                    )}
                  </div>
                  <p className="mb-space-10 text-base">{comment.content}</p>
                  <div className="gap-space-15 text-size-sm text-gray-3 flex items-center">
                    <button
                      className="gap-space-6 hover:text-dark-deep flex items-center"
                      onClick={() => handleLikePost(comment.id)}
                    >
                      <SvgIcon name="HeartIcon" className="size-4" /> {comment.likes}
                    </button>
                    <button
                      className="hover:text-dark-deep"
                      onClick={() => {
                        setReplyToCommentId(comment.id);
                        setShowReplyInput(true);
                      }}
                    >
                      Reply
                    </button>
                    <span className="text-gray-3 text-size-xs">
                      {toRelativeTime(comment.createdAt)} ago
                    </span>
                  </div>
                  {showReplyInput && replyToCommentId === comment.id && (
                    <div className="mt-space-10">
                      <CommentInput parentId={comment.id} />
                    </div>
                  )}
                </div>
              </div>
            </div>
          ))
        )}
      </div>
    </div>
  );
}
