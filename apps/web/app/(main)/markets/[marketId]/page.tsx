'use client';

import { useParams } from 'next/navigation';
import { useState, useEffect } from 'react';
import { pxToRem } from '@repo/ui/lib/utils';
import { useMarketById } from '@/hooks/query/market';
import MarketMetadata from './components/MarketMetadata';
import MarketAuthor from './components/MarketAuthor';
import MarketOutcomes from './components/market-outcomes';
import MarketRules from './components/MarketRules';
import MarketTabs from './components/MarketTabs';
import PredictControlPanel from './components/predict-control-panel';
import DisputeControlPanel from './components/dispute-control-panel';
import MarketStatusStep from './components/market-status-step';
import { ExternalLink } from 'lucide-react';

const calculatePercentage = (optionVolume: string, totalVolume: string): string => {
  const total = parseFloat(totalVolume.replace(/[$,]/g, '')) || 0;
  const option = parseFloat(optionVolume.replace(/[$,]/g, '')) || 0;
  const percentage = total > 0 ? Math.round((option / total) * 100) : 0;
  return `${percentage}%`;
};

export default function MarketDetailPage() {
  const params = useParams();
  const marketId = params.marketId as string;
  const [selectedOutcome, setSelectedOutcome] = useState<{
    text: string;
    volume: string;
    order: number;
  } | null>(null);
  const [marketStatus, setMarketStatus] = useState<'predict' | 'dispute'>('predict');
  const { data, isLoading, isError } = useMarketById(marketId);

  // Set default selectedOutcome to first outcome when data is loaded
  useEffect(() => {
    if (data && data.marketOutcomes && data.marketOutcomes.length > 0 && !selectedOutcome) {
      const firstOutcome = data.marketOutcomes[0];
      if (firstOutcome) {
        setSelectedOutcome({
          text: firstOutcome.outcome,
          volume: firstOutcome.volume,
          order: firstOutcome.order,
        });
      }
    }
  }, [data, selectedOutcome]);

  const handleClickOutcome = ({
    text,
    volume,
    order,
  }: {
    text: string;
    volume: string;
    order: number;
  }) => {
    if (!data) return;
    setSelectedOutcome({ text, volume, order });
  };

  if (isLoading) {
    return <div className="p-space-30">로딩 중...</div>;
  }

  if (isError) {
    return (
      <div className="p-space-30 text-center">
        <div className="mb-2 text-lg font-semibold text-gray-700">Failed to load market</div>
        <div className="text-sm text-gray-500">Please refresh the page or try again later.</div>
      </div>
    );
  }

  if (!data) {
    return <div className="p-space-30">마켓 정보를 찾을 수 없습니다.</div>;
  }

  return (
    <div
      data-page="market-detail"
      style={
        {
          '--aside-width': pxToRem(340),
        } as React.CSSProperties
      }
      className="flex flex-col"
    >
      <div className="flex pr-(--aside-width)">
        <section className="p-space-30 mx-auto max-w-[1300px] flex-1">
          <div className="mb-space-30">
            <div className="flex items-center justify-between">
              <MarketMetadata
                avatarUrl={data.marketAvatarImageUrl}
                title={data.marketTitle}
                endTime={data.marketPredictionDeadline}
              />
              <button
                onClick={() =>
                  setMarketStatus(prev => (prev === 'predict' ? 'dispute' : 'predict'))
                }
                className="text-size-xs bg-gray-2 rounded-sm px-2 py-1"
              >
                Toggle Mode: {marketStatus}
              </button>
            </div>
          </div>

          <div className="pb-space-30 border-line border-b">
            <MarketAuthor
              channelName={data.channelName}
              avatarUrl={data.channelAvatarImageUrl}
              totalVolume={data.marketTotalVolumeFormatted}
              totalPredictors={data.marketParticipants}
              currentPredictions={data.marketPredictCount}
            />
          </div>

          <div className="my-space-30">
            {data.marketOutcomes && data.marketOutcomes.length > 0 && (
              <MarketOutcomes
                handleClickOutcome={handleClickOutcome}
                outcomes={data.marketOutcomes.map(option => ({
                  text: option.outcome,
                  percentage: calculatePercentage(option.volume, data.marketTotalVolumeFormatted),
                  volume: option.volume,
                  order: option.order,
                }))}
              />
            )}
          </div>

          <MarketRules description={data?.marketDescription || ''} />
          <div className="gap-space-10 mt-space-30 flex items-center">
            <span className="text-size-sm text-mid-dark font-semibold">Reference URL</span>
            <svg
              width="1"
              height="10"
              viewBox="0 0 1 10"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path d="M0 10V0H1V10H0Z" fill="#C1C1C1" />
            </svg>
            <a
              href={data.marketReferenceUrl}
              className="text-size-sm text-gray-3 flex items-center underline"
              target="_blank"
              rel="noopener noreferrer"
            >
              {data.marketReferenceUrl}
              <ExternalLink className="text-mid-dark ml-1" size={14} />
            </a>
          </div>
          <MarketTabs marketId={marketId} />
        </section>
      </div>

      <aside className="border-l-line pt-space-30 fixed top-[109px] right-0 bottom-0 z-10 w-(--aside-width) overflow-y-auto border-l bg-white">
        <div className="px-space-30 pb-space-30">
          {data.marketIsDisputable && !!data.marketTopOutcomeVolumeFormatted ? (
            <DisputeControlPanel
              marketId={marketId}
              maxDeposit={parseFloat(data.marketMaxOutcomeVolumeFormatted)}
              proposedOutcome={data.marketProposedOutcome!}
              disputeEndTime={new Date(Date.now() + 7 * 24 * 60 * 60 * 1000)}
              disputeAmount={parseFloat(data.marketTopOutcomeVolumeFormatted)}
            />
          ) : (
            <PredictControlPanel
              marketId={marketId}
              selectedOutcome={selectedOutcome}
            />
          )}
        </div>
        <div className="border-t-line p-space-30 border-t">
          <MarketStatusStep currentStatus={marketStatus} />
        </div>
      </aside>
    </div>
  );
}
