import { marketService } from '@/lib/api/market/market.service';
import { max, toNumber } from 'lodash-es';

type GetMarketsReturnType = Awaited<ReturnType<typeof marketService.getMarkets>>;

export const mapMarketsResponseToMarketsProps = (response: GetMarketsReturnType) => {
  const markets = response.markets.map(market => {
    return {
      marketId: market.id,
      marketTitle: market.title,
      marketAvatarImageUrl: market.imageUrl,
      marketPredictionDeadline: market.predictionDeadline,
      marketConfirmationDeadline: market.resultConfirmDeadline,
      marketTotalVolume: market.rawTotalVolume,
      marketTotalVolumeFormatted: market.formattedTotalVolume,
      marketParticipants: market.totalPredictor,
      marketMaxOutcomeVolume: market.rawMaxOutcomeVolume,
      marketMaxOutcomeVolumeFormatted: market.formattedMaxOutcomeVolume,
      marketStatusText: market.statusText,
      marketOutcomes: market.outcomes,
      channelId: market.channel.id,
      channelName: market.channel.name,
      channelAvatarImageUrl: market.channel.imageUrl,
    };
  });

  return {
    markets,
    totalLength: response.totalLength,
  };
};

export type MarketsProps = ReturnType<typeof mapMarketsResponseToMarketsProps>;
export type MarketListItem = MarketsProps['markets'][number];

type GetMarketReturnType = Awaited<ReturnType<typeof marketService.getMarketById>>;

export const mapMarketResponseToMarketProps = (market: GetMarketReturnType) => {
  const marketTopOutcomeVolume = max(market.outcomes.map(outcome => toNumber(outcome.volume)));
  return {
    marketId: market.id,
    marketTitle: market.title,
    marketDescription: market.description,
    marketReferenceUrl: market.referenceURL,
    marketBroadcastUrl: market.broadcastURL,
    marketCompetitive: market.competitive,
    marketMinPredictCount: market.minPredictCount,
    marketMaxOutcomeVolume: market.rawMaxOutcomeVolume,
    marketMaxOutcomeVolumeFormatted: market.formattedMaxOutcomeVolume,
    marketCreatedAt: market.createdAt,
    marketAvatarImageUrl: market.imageUrl,
    marketPredictionDeadline: market.predictionDeadline,
    marketConfirmationDeadline: market.resultConfirmDeadline,
    marketTotalVolume: market.rawTotalVolume,
    marketTotalVolumeFormatted: market.formattedTotalVolume,
    marketParticipants: market.totalPredictor,
    marketPredictCount: market.predictCount,
    marketStatusText: market.statusText,
    marketOutcomes: market.outcomes,
    marketTopOutcomeVolume: market.rawMaxOutcomeVolume,
    marketTopOutcomeVolumeFormatted: market.formattedMaxOutcomeVolume,
    marketIsDisputable: market.status === 'DISPUTABLE',
    marketOutcomeProposedAt: market.outcomeProposedAt,
    marketProposedOutcome: market.proposedOutcome,
    channelId: market.channel.id,
    channelName: market.channel.name,
    channelAvatarImageUrl: market.channel.imageUrl,
  };
};

export type MarketProps = ReturnType<typeof mapMarketResponseToMarketProps>;
