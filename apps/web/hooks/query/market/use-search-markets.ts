import { useQuery } from '@tanstack/react-query';
import { marketKeys } from '../query-keys';
import { marketService } from '@/lib/api/market/market.service';

export const useSearchMarkets = (query: string, options?: { page?: number; limit?: number }) => {
  return useQuery({
    queryKey: [...marketKeys.search(query), options],
    queryFn: () => marketService.searchMarkets(query, options),
    enabled: !!query && query.length >= 2,
  });
};
