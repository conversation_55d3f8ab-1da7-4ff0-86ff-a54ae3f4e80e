import { marketService } from '@/lib/api/market/market.service';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import useSignCreateMarket, { CreateMarketMessage } from '../../use-sign-create-market';
import { marketKeys } from '../query-keys';

interface CreateMarketFlowOptions {
  onSigningStart?: () => void;
  onSigningComplete?: (signature: string) => void;
  onApiCallStart?: () => void;
  onSuccess?: (data: any) => void;
  onError?: (error: Error, phase: 'signing' | 'api') => void;
}

export const useCreateMarketFlow = (options?: CreateMarketFlowOptions) => {
  const queryClient = useQueryClient();
  const { signCreateMarket } = useSignCreateMarket();

  return useMutation({
    mutationFn: async (data: CreateMarketMessage & { image?: File }) => {
      try {
        // options?.onSigningStart?.();

        const signature = await signCreateMarket({
          ...data,
        });

        // options?.onSigningComplete?.(signature);
        // options?.onApiCallStart?.();

        const apiData = {
          ...data,
          collateralAmount: data.collateralAmount.toString(),
          signature,
        };

        if (data.image) {
          apiData.image = data.image;
        }

        const result = await marketService.createMarket(apiData);
        return result;
      } catch (error) {
        // 어느 단계에서 실패했는지 판단하여 적절한 에러 처리
        const phase =
          error instanceof Error && error.message.includes('signature')
            ? ('signing' as const)
            : ('api' as const);

        options?.onError?.(error as Error, phase);
        throw error;
      }
    },

    onSuccess: data => {
      // 성공 시 쿼리 무효화
      queryClient.invalidateQueries({
        queryKey: marketKeys.markets,
      });
      options?.onSuccess?.(data);
    },
  });
};
