import { useQuery } from '@tanstack/react-query';
import { marketKeys } from '../query-keys';
import { marketService } from '@/lib/api/market/market.service';

export const useMarketsByCategory = (
  category: string,
  tag?: string,
  options?: {
    page?: number;
    limit?: number;
    order?: 'VOLUME' | 'NEWEST' | 'ENDING_SOON' | 'COMPETITIVE';
  }
) => {
  return useQuery({
    queryKey: [...marketKeys.markets, category, tag, options],
    queryFn: () => marketService.getMarketByCategory(category, tag, options),
    enabled: !!category,
  });
};
