import { useQuery } from '@tanstack/react-query';
import { marketKeys } from '../query-keys';
import { marketService } from '@/lib/api/market/market.service';
import { GetMarketsWithStatusRequestQuery } from '@/lib/api/market/market.schema.server';
import { mapMarketsResponseToMarketsProps } from './market.mapper';

export const useMarketsWithStatusFilter = (options?: GetMarketsWithStatusRequestQuery) => {
  return useQuery({
    queryKey: marketKeys.marketsWithStatus(options),
    queryFn: () => marketService.getMarketsWithStatusFilter(options),
    select: mapMarketsResponseToMarketsProps,
  });
};
