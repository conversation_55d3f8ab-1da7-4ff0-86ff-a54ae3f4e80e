import { useMutation, useQueryClient } from '@tanstack/react-query';
import { predictService } from '@/lib/api/predict/predict.service';
import { RedeemRequestBody } from '@/lib/api/predict/predict.schema';
import { ClientError } from '@/lib/error';
import { useGlobalStore } from '@/store/global.store';

export const useRedeemAndGo = () => {
  const queryClient = useQueryClient();
  const { safeSmartAccount } = useGlobalStore();

  return useMutation({
    mutationFn: (redeemData: RedeemRequestBody) => {
      if (!safeSmartAccount) {
        throw ClientError.fromQuery(
          'SafeSmartAccount is not available. Please ensure you are connected to a wallet.'
        );
      }

      return predictService.redeemAndGo(safeSmartAccount, redeemData);
    },
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({
        queryKey: ['market', variables.marketId],
      });
    },
  });
};
