import { useMutation, useQueryClient } from '@tanstack/react-query';
import { predictService } from '@/lib/api/predict/predict.service';
import { ClaimRequestBody } from '@/lib/api/predict/predict.schema';
import { ClientError } from '@/lib/error';
import { useGlobalStore } from '@/store/global.store';

export const useClaimAndGo = () => {
  const queryClient = useQueryClient();
  const { safeSmartAccount } = useGlobalStore();

  return useMutation({
    mutationFn: (data: ClaimRequestBody) => {
      if (!safeSmartAccount) {
        throw ClientError.fromQuery(
          'SafeSmartAccount is not available. Please ensure you are connected to a wallet.'
        );
      }

      return predictService.claimAndGo(safeSmartAccount, data);
    },
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({
        queryKey: ['market', variables.marketId],
      });
    },
  });
};
