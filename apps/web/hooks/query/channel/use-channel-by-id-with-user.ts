import { useQuery } from '@tanstack/react-query';
import { channelKeys } from '../query-keys';
import { channelService } from '@/lib/api/channel/channel.service';
import { ChannelInfoResponse } from '@/lib/api/channel/channel.schema.server';

export type ChannelByIdWithUser = {
  channelBannerUrl: string;
  channelAvatarUrl: string;
  channelName: string;
  channelDescription: string;
  channelSubscribers: number;
  channelTotalVolume: string;
  channelTotalMarkets: number;
  channelIsLeaderLive: boolean;
  channelIsSubscribed: boolean;
  channelSns: ChannelInfoResponse['channelSns'];
  userNickname: string;
  userAddress: string;
};

export const useChannelByIdWithUser = (channelId: string) => {
  return useQuery({
    queryKey: channelKeys.channelWithUser(channelId),
    queryFn: () => channelService.getChannelByIdWithUser(channelId),
    enabled: !!channelId,
    select: data => {
      return {
        channelBannerUrl: data.channel.bannerUrl,
        channelAvatarUrl: data.channel.imageUrl,
        channelName: data.channel.name,
        channelDescription: data.channel.description,
        channelSubscribers: data.channel.subscribers,
        channelTotalVolume: data.channel.totalVolume,
        channelTotalMarkets: data.channel.totalMarkets,
        channelIsLeaderLive: data.channel.isLeaderLive,
        channelIsSubscribed: data.channel.isSubscribed,
        channelSns: data.channel.channelSns,
        userNickname: data.user?.nickname,
        userAddress: data.user?.address,
      };
    },
  });
};
