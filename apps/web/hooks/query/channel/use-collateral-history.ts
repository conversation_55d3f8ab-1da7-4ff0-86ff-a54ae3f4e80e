import { useQuery } from '@tanstack/react-query';
import { channelKeys } from '../query-keys';
import { channelService } from '@/lib/api/channel/channel.service';

export const useCollateralHistory = (options?: { page?: number; limit?: number }) => {
  return useQuery({
    queryKey: [...channelKeys.collateralHistory, options],
    queryFn: () => channelService.getCollateralHistory(options),
  });
};
