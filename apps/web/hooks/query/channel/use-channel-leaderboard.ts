import { useQuery } from '@tanstack/react-query';
import { channelKeys } from '../query-keys';
import { channelService } from '@/lib/api/channel/channel.service';
import { LeaderboardType } from '@/lib/api/leaderboard/leaderboard.schema.server';

type GetChannelLeaderboardReturnType = Awaited<
  ReturnType<typeof channelService.getChannelLeaderboard>
>;

export const mapChannelLeaderboardResponseToChannelLeaderboardProps = (
  response: GetChannelLeaderboardReturnType
) => {
  const rankings = response.rankings.map(item => ({
    userRank: item.rank,
    userAddress: item.address,
    userNickname: item.nickname,
    userPnl: item.pnl,
    userVolume: item.volume,
    userAvatarurl: '',
  }));
  return { rankings, myRank: response.myRank ?? null };
};

export type ChannelLeaderboardProps = ReturnType<
  typeof mapChannelLeaderboardResponseToChannelLeaderboardProps
>;

export const useChannelLeaderboard = (channelId: string, type: LeaderboardType) => {
  return useQuery({
    queryKey: channelKeys.channelLeaderboard(channelId, type),
    queryFn: () => channelService.getChannelLeaderboard(channelId, type),
    enabled: !!channelId,
    select: mapChannelLeaderboardResponseToChannelLeaderboardProps,
  });
};

export const useChannelLeaderboardByProfit = (channelId: string) => {
  return useChannelLeaderboard(channelId, 'profit');
};
