import { useQuery } from '@tanstack/react-query';
import { channelKeys } from '../query-keys';
import { channelService } from '@/lib/api/channel/channel.service';
import { RewardsOrder } from '@/lib/api/channel/channel.schema';

export const useRewardsHistory = (options?: {
  page?: number;
  limit?: number;
  order?: RewardsOrder;
}) => {
  return useQuery({
    queryKey: [...channelKeys.rewardsHistory, options],
    queryFn: () => channelService.getRewardsHistory(options),
  });
};
