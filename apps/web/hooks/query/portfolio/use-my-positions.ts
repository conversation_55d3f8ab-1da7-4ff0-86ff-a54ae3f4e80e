import { useQuery } from '@tanstack/react-query';
import { portfolioService } from '@/lib/api/portfolio/portfolio.service';
import { PositionFilter, PositionOrder } from '@/lib/api/portfolio/portfolio.schema.server';
import { transformPostionsData } from '@/app/(with-aside)/positions/transformer';
import { portfolioKeys } from '../query-keys';

export const useMyPositions = (options?: {
  page?: number;
  limit?: number;
  filter?: PositionFilter;
  order?: PositionOrder;
}) => {
  return useQuery({
    queryKey: portfolioKeys.myPositions(options),
    queryFn: async () => {
      const res = await portfolioService.getPositions(options);
      return transformPostionsData(res.positions);
    },
  });
};
