import { useQuery } from '@tanstack/react-query';
import { portfolioService } from '@/lib/api/portfolio/portfolio.service';
import { PositionFilter, PositionOrder } from '@/lib/api/portfolio/portfolio.schema.server';
import { portfolioKeys } from '../query-keys';

export type PortfolioPositionItem = {
  marketId: string;
  marketTitle: string;
  marketImageUrl: string;
  outcome: string;
  outcomeOrder: number;
  value: string;
  estimatedOdds: string;
  estimatedWin: string;
  marketStatus: string;
};

export const usePortfolioPositions = (options?: {
  page?: number;
  limit?: number;
  filter?: PositionFilter;
  order?: PositionOrder;
}) => {
  return useQuery({
    queryKey: portfolioKeys.positions(options),
    queryFn: () => portfolioService.getPositions(options),
    select: data => {
      const positions = data.positions.map(position => {
        return {
          marketId: position.market.id,
          marketTitle: position.market.title,
          marketImageUrl: position.market.imageUrl,
          marketStatus: position.market.status ?? '',
          outcome: position.outcome,
          outcomeOrder: position.outcomeOrder,
          value: position.formattedValue,
          estimatedOdds: position.formattedEstimatedOdds,
          estimatedWin: position.formattedEstimatedWin,
        };
      });

      return {
        positions,
        totalLength: data.totalLength,
        hasNext: data.hasNext,
      };
    },
  });
};
