import { useQuery } from '@tanstack/react-query';
import { portfolioService } from '@/lib/api/portfolio/portfolio.service';
import { ActivityFilter, ActivityOrder } from '@/lib/api/portfolio/portfolio.schema.server';
import { portfolioKeys } from '../query-keys';
import { toRelativeTime, toTxUrl } from '@/lib/utils';

export type PortfolioActivityItem = {
  marketId: string;
  marketTitle: string;
  marketImageUrl: string;
  outcome: string;
  outcomeOrder: string;
  activityType: string;
  activityAmount: string;
  activityDate: string;
  relativeTime: string;
  txHash: string;
  txUrl: string;
};

export const usePortfolioActivities = (options?: {
  page?: number;
  limit?: number;
  filter?: ActivityFilter;
  order?: ActivityOrder;
}) => {
  return useQuery({
    queryKey: portfolioKeys.activities(options),
    queryFn: () => portfolioService.getActivities(options),
    select: data => {
      const activities = data.activities.map(activity => {
        return {
          marketId: activity.market.id,
          marketTitle: activity.market.title,
          marketImageUrl: activity.market.imageUrl,
          outcome: activity.outcomes[0]?.outcome || '',
          outcomeOrder: activity.outcomes[0]?.outcomeOrder.toString() || '',
          activityType: activity.type,
          activityAmount: activity.formattedAmount,
          activityDate: activity.timestamp,
          relativeTime: toRelativeTime(activity.timestamp),
          txHash: activity.transactionHash,
          txUrl: toTxUrl(activity.transactionHash),
        };
      });
      return {
        totalLength: data.totalLength,
        activities,
      };
    },
  });
};
