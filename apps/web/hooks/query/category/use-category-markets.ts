import { useQuery } from '@tanstack/react-query';
import { categoryKeys } from '../query-keys';
import { categoryService } from '@/lib/api/category/category.service';
import { MarketOrder } from '@/lib/api/category/category.schema.server';

export const useCategoryMarkets = (
  category: string,
  tag: string = 'All',
  options?: {
    page?: number;
    limit?: number;
    order?: MarketOrder;
  }
) => {
  return useQuery({
    queryKey: [...categoryKeys.categoryMarkets(category, tag), options],
    queryFn: () => categoryService.getMarketsInCategory(category, tag, options),
    enabled: !!(category && tag),
  });
};
