import { useQuery } from '@tanstack/react-query';
import { referralKeys } from '../query-keys';
import { referralService } from '@/lib/api/referral/referral.service';
import type { GetReferralDetailsRequest } from '@/lib/api/referral/referral.schema.server';

export const useReferralDetails = (options: GetReferralDetailsRequest) => {
  return useQuery({
    queryKey: referralKeys.details(options.type, options),
    queryFn: () => referralService.getReferralDetails(options),
  });
};
