import { InfoButton } from '@/components/ui/base.button';
import SvgIcon from '@/components/icons/svg-icon';
import { toast } from '@/components/ui/base.toast';
import { appKitModal } from '@/components/providers/wagmi-provider';
import { useGlobalStore } from '@/store/global.store';
import { useState, useEffect, useRef } from 'react';
import { XsIcon } from '@/components/icons/xs-icon';
import { network } from '@/lib/web3/wagmi';
import { generateTransferURL } from '@/lib/format';

interface DepositUsdcPopupProps {
  onClose: () => void;
}

export default function DepositUsdcPopup({ onClose }: DepositUsdcPopupProps) {
  const { safeSmartAccount } = useGlobalStore();
  const [showQR, setShowQR] = useState(false);
  const qrRef = useRef<HTMLDivElement>(null);

  const handleCopyAddress = () => {
    navigator.clipboard.writeText(safeSmartAccount?.address ?? '');
    toast.success('Address copied to clipboard!');
  };

  const handleToggleQR = () => {
    setShowQR(!showQR);
  };

  const handleBuyCrypto = () => {
    onClose();
    appKitModal.open({
      view: 'OnRampProviders',
    });
  };

  const handleSuperbridge = () => {
    window.open('https://superbridge.app/base', '_blank');
  };

  useEffect(() => {
    if (showQR && safeSmartAccount?.address && qrRef.current) {
      // Clear previous QR code
      qrRef.current.innerHTML = '';

      const usdcTransferURL = generateTransferURL(safeSmartAccount.address);
      console.log('usdcTransferURL', usdcTransferURL);
      // Import QRCodeStyling dynamically for client-side rendering
      import('qr-code-styling').then(({ default: QRCodeStyling }) => {
        const qrCode = new QRCodeStyling({
          width: 200,
          height: 200,
          type: 'svg',
          data: usdcTransferURL,
          // qrOptions: {
          //   typeNumber: 0,
          //   mode: 'Byte',
          //   errorCorrectionLevel: 'Q',
          // },
          image: '/base-network-logo.svg',
          imageOptions: {
            hideBackgroundDots: true,
            imageSize: 0.3,
            margin: 1,
          },
          dotsOptions: {
            color: '#000000', // Base blue color
            type: 'rounded',
          },
          backgroundOptions: {
            color: '#ffffff',
          },
          cornersSquareOptions: {
            color: '#000000',
            type: 'rounded',
          },
          cornersDotOptions: {
            color: '#000000',
            type: 'rounded',
          },
        });

        if (qrRef.current) {
          qrCode.append(qrRef.current);
        }
      });
    }
  }, [showQR, safeSmartAccount?.address]);

  return (
    <div className="bg-gray-2 w-full">
      {/* Header */}
      <div className="py-space-50 px-space-40 gap-space-40 flex flex-col text-center">
        <div className="gap-space-20 flex flex-col">
          <h1 className="text-size-lg font-semibold">Deposit USDC</h1>
          <p className="text-size-xs text-gray-3">
            Only deposit <span className="text-sky font-medium">Base USDC</span> to this address.
          </p>
        </div>

        {/* Address Input */}
        <div className="border-line rounded-round-lg p-space-12 gap-space-8 flex items-center border bg-white">
          <input
            type="text"
            value={safeSmartAccount?.address ?? ''}
            readOnly
            className="text-size-xs text-gray-4 flex-1 border-none bg-transparent font-mono outline-none"
          />
          <button
            onClick={handleCopyAddress}
            className="p-space-6 hover:bg-gray-1 rounded-round-sm transition-colors"
          >
            <XsIcon name="copy" />
          </button>
          <button
            onClick={handleToggleQR}
            className="p-space-6 hover:bg-gray-1 rounded-round-sm transition-colors"
          >
            <XsIcon name="qr_code" />
          </button>
        </div>

        {/* QR Code Section */}
        {showQR && safeSmartAccount?.address && (
          <div className="border-line rounded-round-lg p-space-20 gap-space-12 flex flex-col items-center border bg-white">
            <p className="text-size-xs text-gray-3 font-medium">Scan QR Code for USDC Transfer</p>
            <div className="p-space-8 rounded-round-sm border border-gray-100 bg-white">
              <div ref={qrRef} className="flex items-center justify-center" />
            </div>
            <div className="space-y-1 text-center">
              <p className="text-size-xs text-gray-3">Scan with MetaMask or compatible wallet</p>
              <p className="text-size-xxs text-gray-4">
                EIP-681 compliant • Base Chain • USDC Transfer
              </p>
            </div>
          </div>
        )}

        {/* Options */}
        <div className="gap-space-16 flex flex-col">
          {/* Buy Crypto Option */}
          <button
            onClick={handleBuyCrypto}
            className="border-line rounded-round-lg p-space-16 gap-space-12 hover:bg-gray-1 flex items-center border bg-white text-left transition-colors"
          >
            <div className="bg-gray-3 rounded-round-sm flex h-8 w-8 items-center justify-center">
              <svg
                width="16"
                height="16"
                viewBox="0 0 16 16"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M8 0C12.418 0 16 3.582 16 8C16 12.418 12.418 16 8 16C3.582 16 0 12.418 0 8C0 3.582 3.582 0 8 0ZM5.6 6.4V9.6H10.4V6.4H5.6ZM8 2.4C5.348 2.4 3.2 4.548 3.2 7.2V8.8C3.2 11.452 5.348 13.6 8 13.6C10.652 13.6 12.8 11.452 12.8 8.8V7.2C12.8 4.548 10.652 2.4 8 2.4Z"
                  fill="white"
                />
              </svg>
            </div>
            <div className="flex-1">
              <div className="text-size-sm font-semibold">Buy Crypto</div>
              <div className="text-size-xs text-gray-3">Easy with card or bank account</div>
            </div>
          </button>

          {/* Superbridge Option */}
          <button
            onClick={handleSuperbridge}
            className="border-line rounded-round-lg p-space-16 gap-space-12 hover:bg-gray-1 flex items-center border bg-white text-left transition-colors"
          >
            <div className="flex h-8 w-8 items-center justify-center">
              <svg
                width="32"
                height="32"
                viewBox="0 0 32 32"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <rect width="32" height="32" rx="16" fill="black" />
                <path
                  d="M24 16C24 20.418 20.418 24 16 24C11.582 24 8 20.418 8 16C8 11.582 11.582 8 16 8C20.418 8 24 11.582 24 16Z"
                  fill="white"
                />
                <path
                  d="M19.2 13.6L16 16.8L12.8 13.6L11.2 15.2L16 20L20.8 15.2L19.2 13.6Z"
                  fill="black"
                />
              </svg>
            </div>
            <div className="flex-1">
              <div className="text-size-sm font-semibold">SUPERBRIDGE</div>
              <div className="text-size-xs text-gray-3">
                Easily bridge your Ethereum assets to Base.
              </div>
              <div className="text-size-xs text-sky">https://superbridge.app/base</div>
            </div>
          </button>
        </div>
      </div>

      {/* Continue Button */}
      <div className="p-0">
        <InfoButton onClick={onClose} size="lg" fontSize="sm" rounded="none" className="w-full">
          Continue
        </InfoButton>
      </div>
    </div>
  );
}
