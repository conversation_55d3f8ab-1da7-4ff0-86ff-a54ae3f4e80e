import SvgIcon from '@/components/icons/svg-icon';
import { InfoButton, NeutralButton } from '@/components/ui/base.button';
import { BaseInput } from '@/components/ui/base.input';
import { BaseTextarea } from '@/components/ui/base.textarea';
import { useMyUSDCBalance } from '@/hooks/use-usdc-balance';
import { zodResolver } from '@hookform/resolvers/zod';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@repo/ui/components/form';
import { Slider } from '@repo/ui/components/slider';
import { cn } from '@repo/ui/lib/utils';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

const DisputeFormSchema = z.object({
  deposit: z.number().min(1, 'Deposit amount is required'),
  description: z.string().optional(),
  referenceUrl: z.string().optional(),
});

type DisputeFormValues = z.infer<typeof DisputeFormSchema>;

interface FileUploadProps {
  onFileSelect: (files: File[]) => void;
  multiple?: boolean;
  accept?: string;
  disabled?: boolean;
}

function FileUpload({
  onFileSelect,
  multiple = true,
  accept = 'image/*,.pdf,.doc,.docx',
  disabled = false,
}: FileUploadProps) {
  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      const fileArray = Array.from(e.target.files);
      setSelectedFiles(prev => [...prev, ...fileArray]);
      onFileSelect(fileArray);
    }
  };

  const handleRemoveFile = (index: number) => {
    setSelectedFiles(prev => {
      const newFiles = [...prev];
      newFiles.splice(index, 1);
      return newFiles;
    });
  };

  return (
    <div>
      <div className="">
        <label className="gap-space-10 flex cursor-pointer items-center">
          <NeutralButton
            size="xs"
            fontSize="xs"
            type="button"
            className="px-space-15 font-semibold"
            onClick={() => document.getElementById('file-upload')?.click()}
            disabled={disabled}
          >
            Browse Files
          </NeutralButton>
          <input
            type="file"
            id="file-upload"
            className="hidden"
            onChange={handleFileChange}
            multiple={multiple}
            accept={accept}
            disabled={disabled}
          />
        </label>

        {selectedFiles.length > 0 && (
          <div className="gap-space-5 flex flex-col">
            {selectedFiles.map((file, index) => (
              <div
                key={index}
                className="gap-space-10 p-space-10 flex items-center justify-between bg-white"
              >
                <div className="text-size-xs truncate">{file.name}</div>
                <button
                  onClick={() => handleRemoveFile(index)}
                  className="text-gray-3 hover:text-no-red"
                  disabled={disabled}
                >
                  <SvgIcon name="XIcon" className="size-4" />
                </button>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}

interface OpenDisputePopupProps {
  isPending?: boolean;
  onSubmit?: (data: {
    amount: number;
    description?: string;
    referenceUrl?: string;
    fileURLs?: string[];
  }) => void;
  onCancel?: () => void;
  className?: string;
  minAmount?: number;
  maxAmount?: number;
  balance?: number;
  disputeAmount: number;
}

export default function OpenDisputePopupBody({
  isPending = false,
  onSubmit,
  onCancel,
  minAmount = 1,
  maxAmount = 10,
  disputeAmount,
}: OpenDisputePopupProps) {
  const [files, setFiles] = useState<File[]>([]);
  const { balance } = useMyUSDCBalance();

  const form = useForm<DisputeFormValues>({
    resolver: zodResolver(DisputeFormSchema),
    defaultValues: {
      deposit: minAmount,
      description: '',
      referenceUrl: '',
    },
  });

  const { watch, setValue } = form;
  const deposit = watch('deposit');

  const handleDepositChange = (value: number[]) => {
    if (value && value.length > 0) {
      setValue('deposit', value[0] as number);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseFloat(e.target.value);
    if (isNaN(value)) {
      setValue('deposit', minAmount);
    } else {
      setValue('deposit', Math.min(Math.max(value, minAmount), maxAmount));
    }
  };

  const handleAddAmount = (amount: number) => {
    setValue('deposit', Math.min(deposit + amount, maxAmount));
  };

  const handleSubtractAmount = (amount: number) => {
    setValue('deposit', Math.max(deposit - amount, minAmount));
  };

  const handleFileSelect = (newFiles: File[]) => {
    setFiles(prev => [...prev, ...newFiles]);
  };

  const onFormSubmit = (values: DisputeFormValues) => {
    if (onSubmit) {
      // Convert files to URLs if needed (for now, we'll pass empty fileURLs)
      const fileURLs: string[] = []; // This would typically be handled by file upload service

      onSubmit({
        amount: values.deposit,
        description: values.description,
        referenceUrl: values.referenceUrl,
        fileURLs,
      });
    }
  };

  return (
    <div className={cn('w-full')}>
      {/* Header */}
      <div className="mb-space-30 text-center">
        <h1 className="text-size-base font-bold">Open a Dispute</h1>
        <p className="text-size-xs text-gray-3 mt-space-10">
          If a dispute regarding the results is upheld,
          <br />
          PredictGo will conduct a final review and confirm the results.
        </p>
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onFormSubmit)} className="gap-space-30 flex flex-col">
          {/* Deposit */}
          <FormField
            control={form.control}
            name="deposit"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-size-xs font-semibold">
                  Deposit &nbsp;
                  <span className="text-no-red">(Required)</span>
                </FormLabel>
                <div className="gap-space-15 flex flex-col">
                  <div className="gap-space-10 flex">
                    <div className="flex flex-col justify-between">
                      <Slider
                        defaultValue={[minAmount]}
                        value={[field.value]}
                        min={minAmount}
                        max={maxAmount}
                        step={1}
                        onValueChange={handleDepositChange}
                        disabled={isPending}
                      />
                      <div className="mt-space-5 flex gap-1.5">
                        <NeutralButton
                          fontSize="xxs10"
                          rounded="none"
                          className="px-space-5"
                          size="xxs"
                          type="button"
                          onClick={() => handleSubtractAmount(10)}
                          disabled={isPending}
                        >
                          -
                        </NeutralButton>
                        <NeutralButton
                          className="px-space-5"
                          fontSize="xxs10"
                          size="xxs"
                          type="button"
                          onClick={() => handleAddAmount(1)}
                          disabled={isPending}
                        >
                          +
                        </NeutralButton>
                      </div>
                    </div>
                    <FormControl>
                      <div>{disputeAmount}</div>
                      {/* <BaseInput
                        value={field.value}
                        onChange={handleInputChange}
                        min={minAmount}
                        max={maxAmount}
                        className="text-size-base text-mid-dark flex-1 text-right font-bold"
                        disabled={isPending}
                      /> */}
                    </FormControl>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-size-xs text-gray-3">My balance: ${balance}</span>
                    <span className="text-size-xs text-gray-3">Minimum: ${minAmount}</span>
                  </div>
                </div>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Additional Description */}
          <FormField
            control={form.control}
            name="description"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-size-xs font-semibold">
                  Additional Description <span className="text-yes-green">(Optional)</span>
                </FormLabel>
                <FormControl>
                  <BaseTextarea
                    placeholder="Please write any additional content you would like to explain. (max. 1,000 characters)"
                    {...field}
                    disabled={isPending}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Reference URL */}
          <FormField
            control={form.control}
            name="referenceUrl"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-size-xs font-semibold">
                  Reference URL &nbsp;
                  <span className="text-yes-green">(Optional)</span>
                </FormLabel>
                <FormControl>
                  <BaseInput
                    placeholder="Please enter a URL related to the prediction."
                    {...field}
                    disabled={isPending}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Upload Files */}
          <div>
            <h2 className="text-size-xs mb-space-15 font-semibold">
              Upload Files &nbsp;
              <span className="text-yes-green">(Optional)</span>
            </h2>
            <div className="text-size-xs border-line bg-gray-2 text-gray-3 py-space-20 flex flex-col items-center border font-semibold">
              <p className="mb-space-10">Choose a file or drag and drop it here</p>
              <p className="mb-space-20">JPG, PNG and MP4 formats, up to 50MB</p>
              <FileUpload onFileSelect={handleFileSelect} disabled={isPending} />
            </div>
          </div>

          {/* Caution Text */}
          <div className="gap-space-5 mb-space-40 flex items-start">
            <SvgIcon name="AlertIcon" className="text-yes-green size-[14px] flex-shrink-0" />
            <p className="text-size-xxs text-yes-green">
              The submitted information is for reference only and may not affect the final outcome.
            </p>
          </div>

          {/* Button */}
          <div className="flex">
            {onCancel && (
              <NeutralButton
                onClick={onCancel}
                size="lg"
                fontSize="xs"
                rounded="none"
                className="flex-1"
                disabled={isPending}
                type="button"
              >
                Cancel
              </NeutralButton>
            )}
            <InfoButton
              rounded="none"
              className={onCancel ? 'flex-1' : 'w-full'}
              disabled={isPending}
              type="submit"
            >
              {isPending ? 'Opening Dispute...' : 'Open Dispute'}
            </InfoButton>
          </div>
        </form>
      </Form>
    </div>
  );
}
