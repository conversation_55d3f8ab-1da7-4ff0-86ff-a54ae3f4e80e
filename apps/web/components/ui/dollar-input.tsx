import { cn } from '@repo/ui/lib/utils';
import React, { useCallback, useState } from 'react';
import type { ComponentProps } from 'react';
import { BaseInput } from './base.input';

interface DollarInputProps extends Omit<ComponentProps<typeof BaseInput>, 'value' | 'onChange'> {
  value?: number;
  onChange?: (value: number) => void;
  maxValue?: number;
  minValue?: number;
}

const formatCurrency = (value: number): string => {
  return `$${value.toLocaleString()}`;
};

const formatInputValue = (value: number): string => {
  if (value === 0) return '';
  return formatCurrency(value);
};

export function DollarInput({
  className,
  value = 0,
  onChange,
  maxValue,
  minValue = 0,
  placeholder = '$0',
  ...props
}: DollarInputProps) {
  const handleInputChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const inputValue = e.target.value.replace(/[$,]/g, '');
      const numericValue = parseFloat(inputValue) || 0;

      // Apply validation constraints
      let validatedValue = numericValue;

      if (validatedValue < minValue) {
        validatedValue = minValue;
      }

      if (maxValue !== undefined && validatedValue > maxValue) {
        validatedValue = maxValue;
      }

      onChange?.(validatedValue);
    },
    [onChange, maxValue, minValue]
  );

  return (
    <BaseInput
      className={cn(className)}
      value={formatInputValue(value)}
      onChange={handleInputChange}
      placeholder={placeholder}
      type="text"
      {...props}
    />
  );
}
