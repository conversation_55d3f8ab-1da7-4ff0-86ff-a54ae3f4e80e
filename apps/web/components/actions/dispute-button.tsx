import { useDepositDisputeCollateralAndGo } from '@/hooks/query/predict';
import { ButtonCustomSize, InfoButton } from '../ui/base.button';
import { cn } from '@repo/ui/lib/utils';
import OpenDisputePopupBody from '@/components/ui/popup/open-dispute-popup';
import { Popup } from '@/components/ui/popup';
import { toast } from '../ui/base.toast';
import { TEXT_SIZE_CSS_VARS } from '@/lib/constants';
import { toAmount } from '@/lib/format';
import { useState } from 'react';

interface DisputeButtonProps {
  marketId: string;
  className?: string;
  onSuccess?: () => void;
  onFailure?: () => void;
  size?: ButtonCustomSize;
  textSize?: keyof typeof TEXT_SIZE_CSS_VARS;
  minDeposit?: number;
  maxDeposit?: number;
  balance?: number;
  disputeAmount: number;
}

export default function DisputeButton({
  marketId,
  className,
  onSuccess,
  onFailure,
  size,
  textSize,
  minDeposit = 10,
  maxDeposit = 1000,
  balance = 2500,
  disputeAmount,
}: DisputeButtonProps) {
  const [isPopupOpen, setIsPopupOpen] = useState(false);
  const depositDisputeCollateralMutation = useDepositDisputeCollateralAndGo();

  const handleClick = () => {
    setIsPopupOpen(true);
  };

  const closePopup = () => {
    setIsPopupOpen(false);
  };

  const handleSubmit = async (data: {
    amount: number;
    description?: string;
    referenceUrl?: string;
    fileURLs?: string[];
  }) => {
    try {
      const requestData = {
        marketId,
        amount: toAmount(data.amount),
        description: data.description,
        referenceURL: data.referenceUrl,
        fileURLs: data.fileURLs,
      };

      await depositDisputeCollateralMutation.mutateAsync(requestData);
      toast.success('Dispute opened successfully');
      closePopup();
      onSuccess?.();
    } catch (error) {
      console.error('Failed to open dispute:', error);
      toast.error('Failed to open dispute');
      onFailure?.();
    }
  };

  return (
    <>
      <InfoButton
        fontSize={textSize}
        className={cn('w-full', className)}
        onClick={handleClick}
        disabled={depositDisputeCollateralMutation.isPending}
        size={size}
      >
        Open Dispute
      </InfoButton>

      <Popup isOpen={isPopupOpen} onClose={closePopup}>
        <OpenDisputePopupBody
          isPending={depositDisputeCollateralMutation.isPending}
          disputeAmount={disputeAmount}
          minAmount={minDeposit}
          maxAmount={maxDeposit}
          balance={balance}
          onCancel={closePopup}
          onSubmit={handleSubmit}
        />
      </Popup>
    </>
  );
}
