'use client';
import CopyAddress from '@/components/common/copy-address';
import CommonAvatar from '@/components/ui/avatar-image';
import { useUserInfo } from '@/hooks/query/user/use-user-info';
import { Address } from 'viem';

interface UserInfoSectionProps {
  userAddress: Address;
}

export default function UserInfoSection({ userAddress }: UserInfoSectionProps) {
  const { data: user, isLoading, error } = useUserInfo(userAddress);

  if (isLoading) {
    return (
      <section className="gap-space-30 flex items-center">
        {/* Avatar Skeleton */}
        <div className="h-20 w-20 animate-pulse rounded-full bg-gray-300" />

        {/* User Info Skeleton */}
        <div className="gap-space-8 flex flex-col">
          <div className="gap-space-10 flex flex-col">
            <div className="h-6 w-48 animate-pulse rounded bg-gray-300" />
            <div className="h-4 w-64 animate-pulse rounded bg-gray-300" />
          </div>
          <div className="gap-space-10 text-size-xs text-gray-3 flex items-center">
            <div className="h-4 w-16 animate-pulse rounded bg-gray-300" />
            <div className="h-4 w-96 animate-pulse rounded bg-gray-300" />
            <div className="h-4 w-32 animate-pulse rounded bg-gray-300" />
          </div>
        </div>
      </section>
    );
  }

  if (error || !user) {
    return (
      <section className="gap-space-30 flex items-center">
        <div className="h-20 w-20 rounded-full bg-gray-300" />
        <div className="gap-space-8 flex flex-col">
          <div className="text-dark-deep text-size-base font-bold">
            {error ? 'Error loading user data' : 'No user data available'}
          </div>
        </div>
      </section>
    );
  }

  const { address, nickname, imageUrl, bio } = user;

  return (
    <section className="gap-space-30 flex items-center">
      {/* Avatar */}
      <CommonAvatar size="lg" imageUrl={imageUrl} />

      {/* User Info */}
      <div className="gap-space-8 flex flex-col">
        <div className="gap-space-10 flex flex-col">
          <div className="text-dark-deep text-size-base font-bold">{nickname}</div>
          <div className="text-size-xs text-dark-deep">{bio || `Hi, I'm ${nickname}`}</div>
        </div>
        <div className="gap-space-10 text-size-xs text-gray-3 flex items-center">
          <span>Address</span>
          <CopyAddress address={address} />
          <span>
            Joined{' '}
            {new Date().toLocaleDateString('en-GB', {
              day: '2-digit',
              month: 'short',
              year: 'numeric',
            })}
          </span>
        </div>
      </div>
    </section>
  );
}
