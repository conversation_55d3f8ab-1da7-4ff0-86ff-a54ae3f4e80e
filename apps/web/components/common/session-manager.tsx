'use client';

import { useAppKitAccount } from '@reown/appkit/react';
import { useSafeSmartAccount } from '@/hooks/query/use-safe-smart-account';
import { useEffect, useState } from 'react';
import { Popup } from '@/components/ui/popup';
import SmartAccountWarningPopup from '@/components/ui/popup/smart-account-warning-popup';

/**
 * 세션 관리 컴포넌트
 * 앱 전체에서 세션 상태를 관리하고 지갑 연결 상태와 동기화
 * SafeSmartAccount도 세션 주소 기반으로 관리
 */
export const SessionManager = () => {
  const { embeddedWalletInfo } = useAppKitAccount({ namespace: 'eip155' });
  const [isSmartAccountWarningOpen, setIsSmartAccountWarningOpen] = useState(false);
  useSafeSmartAccount();

  const isUserSmartAccount = embeddedWalletInfo?.accountType === 'smartAccount';

  const handleClosePopup = () => {
    // 스마트 계정일 때는 닫을 수 없음
    if (!isUserSmartAccount) {
      setIsSmartAccountWarningOpen(false);
    }
  };

  // 스마트 계정 경고 팝업 관리
  useEffect(() => {
    if (isUserSmartAccount) {
      setIsSmartAccountWarningOpen(true);
    } else {
      setIsSmartAccountWarningOpen(false);
    }
  }, [isUserSmartAccount, embeddedWalletInfo]);

  return (
    <>
      {isUserSmartAccount && (
        <Popup isOpen={isSmartAccountWarningOpen} onClose={handleClosePopup}>
          <SmartAccountWarningPopup />
        </Popup>
      )}
    </>
  );
};
