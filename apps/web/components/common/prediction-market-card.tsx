'use client';
import SvgIcon from '@/components/icons/svg-icon';
import { cardSwitchVariants } from '@/lib/animationVariants';
import { MarketOutcome } from '@/lib/types';
import { formatVolume } from '@repo/shared/utils/number-format';
import { Card, CardContent, CardFooter, CardHeader } from '@repo/ui/components/card';
import { ScrollArea } from '@repo/ui/components/scroll-area';
import { cn } from '@repo/ui/lib/utils';
import { AnimatePresence, motion } from 'motion/react';
import Link from 'next/link';
import { useState } from 'react';
import CommonAvatar from '../ui/avatar-image';
import { MarketCountdown } from './market-countdown';
import PredictionCardQuickMode from './prediction-card-quick-mode';
import PredictionContent from './prediction-content';
import PredictionMarketCardStatus from './prediction-market-card-status';

export interface PredictionMarketCardProps {
  item: {
    marketId: string;
    marketTitle: string;
    marketStatusText: string;
    marketAvatarImageUrl: string;
    marketPredictionDeadline: Date | string | number;
    marketConfirmationDeadline: Date | string | number;
    marketTotalVolume: string;
    marketParticipants: number;
    marketOutcomes: MarketOutcome[];
    marketMaxOutcomeVolume: string;
    channelId: string;
    channelName: string;
    channelAvatarImageUrl: string;
  };
}
export function PredictionMarketCard({ item }: PredictionMarketCardProps) {
  const {
    marketId,
    marketTitle,
    marketAvatarImageUrl,
    marketPredictionDeadline,
    marketConfirmationDeadline,
    marketTotalVolume,
    marketParticipants,
    marketOutcomes,
    marketMaxOutcomeVolume,
    channelId,
    channelName,
    channelAvatarImageUrl,
    marketStatusText,
  } = item;

  const [isQuickMode, setIsQuickMode] = useState(false);
  const [selectedOption, setSelectedOption] = useState<MarketOutcome | null>(null);

  const handleClickPredict = (outcome: MarketOutcome) => {
    setSelectedOption(outcome);
    setIsQuickMode(true);
  };

  const showQuickMode = isQuickMode && selectedOption !== null;
  return (
    <AnimatePresence mode="wait">
      {showQuickMode ? (
        <motion.div
          key="quick-mode"
          variants={cardSwitchVariants}
          initial="quickModeInitial"
          animate="quickModeEnter"
          exit="quickModeExit"
        >
          <PredictionCardQuickMode
            marketId={marketId}
            marketTitle={marketTitle}
            marketAvatarImageUrl={marketAvatarImageUrl}
            marketMaxOutcomeVolume={marketMaxOutcomeVolume}
            outcomeText={selectedOption.outcome}
            outcomePercentage={selectedOption.volume + '%'}
            outcomeOrder={selectedOption.order}
            outcomeVolume={selectedOption.volume}
            onBack={() => {
              setIsQuickMode(false);
              setSelectedOption(null);
            }}
          />
        </motion.div>
      ) : (
        <motion.div
          key="market-card"
          variants={cardSwitchVariants}
          initial="marketCardInitial"
          animate="marketCardEnter"
          exit="marketCardExit"
        >
          <Card
            data-name="market-card"
            className={cn(
              'h-(--market-card-height) min-w-(--market-card-width) justify-between gap-[10px] rounded-none border-0 p-[10px] shadow-none'
            )}
          >
            <MarketCardHeader
              imageUrl={marketAvatarImageUrl}
              marketId={marketId}
              title={marketTitle}
              volume={marketTotalVolume}
              participants={marketParticipants}
            />
            <CardContent className="h-[102px] p-0">
              <ScrollArea className="bg-gray-2 h-full">
                <PredictionContent
                  outcomes={marketOutcomes}
                  handleClick={handleClickPredict}
                  totalVolume={marketTotalVolume}
                />
              </ScrollArea>
            </CardContent>
            <MarketCardFooter
              channelId={channelId}
              marketAvatarImageUrl={channelAvatarImageUrl}
              channelName={channelName}
              status={marketStatusText}
              endDate={marketPredictionDeadline}
            />
          </Card>
        </motion.div>
      )}
    </AnimatePresence>
  );
}

function MarketCardHeader({
  title,
  volume,
  participants,
  marketId,
  imageUrl,
}: {
  title: string;
  volume: string;
  participants: number;
  marketId: string;
  imageUrl: string;
}) {
  return (
    <CardHeader className="gap-0 p-0">
      <Link href={`/markets/${marketId}`}>
        <div className="flex h-(--market-header-height) items-center justify-center">
          <div className="flex w-full items-center justify-between gap-3">
            <CommonAvatar size="md" className="rounded-full" imageUrl={imageUrl} />
            <h3 className="text-size-sm line-clamp-2 min-w-0 flex-1 leading-tight font-semibold">
              {title}
            </h3>
            <div className="flex flex-col items-end">
              <span className="text-size-xxs text-muted-foreground">
                Vol. {formatVolume(volume)}
              </span>
              <div className="flex items-center gap-1">
                <SvgIcon name="MemberIcon" />
                <span className="text-size-xxs text-muted-foreground">{participants}</span>
              </div>
            </div>
          </div>
        </div>
      </Link>
    </CardHeader>
  );
}

function MarketCardFooter({
  marketAvatarImageUrl,
  channelName,
  status,
  channelId,
  endDate,
}: {
  marketAvatarImageUrl: string;
  channelName: string;
  status: string;
  endDate: Date | string | number;
  channelId: string;
}) {
  return (
    <CardFooter className="h-(--market-footer-height) justify-between p-0">
      <div className="flex items-center justify-between">
        <Link href={`/channels/${channelId}`}>
          <div className="rounded-round-lg border-line bg-gray-2 gap-space-6 flex items-center justify-center border">
            <CommonAvatar size="sm" imageUrl={marketAvatarImageUrl} className="rounded-full" />
            <div className="text-size-xxs pr-space-12 font-semibold">{channelName}</div>
          </div>
        </Link>
      </div>
      <div className="gap-space-8 flex items-center">
        <PredictionMarketCardStatus status={status} />
        {endDate && <MarketCountdown endTime={endDate} />}
      </div>
    </CardFooter>
  );
}
