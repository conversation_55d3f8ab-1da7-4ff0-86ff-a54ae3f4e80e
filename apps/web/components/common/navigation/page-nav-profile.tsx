import { Avatar, AvatarImage, AvatarFallback } from '@repo/ui/components/avatar';
import { shortenAddress } from '@repo/ui/lib/utils';
import Link from 'next/link';
import SvgIcon from '../../icons/svg-icon';
import PortfolioNav from './portfolio-nav';

import {
  BaseDropdownMenu,
  BaseDropdownMenuTrigger,
  BaseDropdownMenuContent,
  BaseDropdownMenuItem,
} from '@/components/ui/base.dropdown-menu';
import { copyToClipboard } from '@/lib/utils';
import { toast } from '@repo/ui/components/sonner';
import { DepositButton } from './deposit-button';
import { logout } from '@/lib/logout';

type NavigationLink = {
  label: string;
  href: string;
  icon: React.ReactNode;
  onClick?: () => void;
};

const LIKNS: NavigationLink[] = [
  {
    label: 'Positions',
    href: '/positions',
    icon: <SvgIcon name="PositionsIcon" />,
  },
  {
    label: 'Settings',
    href: '/settings/profile',
    icon: <SvgIcon name="SettingsIcon" />,
  },
  {
    label: 'Channel',
    href: '/channels/setting',
    icon: <SvgIcon name="ChannelIcon" />,
  },
  {
    label: 'Referral',
    href: '/referral',
    icon: <SvgIcon name="ReferralIcon" />,
  },
  {
    label: 'Share Bonus',
    href: '/share-bonus',
    icon: <SvgIcon name="ShareBonusIcon" />,
  },
  {
    label: 'Subscription',
    href: '/subscriptions',
    icon: <SvgIcon name="SubscriptionIcon" />,
  },
  {
    label: 'Documentation',
    href: '/documentation',
    icon: <SvgIcon name="DocumentationIcon" />,
  },
  {
    label: 'Support',
    href: '/support',
    icon: <SvgIcon name="SupportIcon" />,
  },
  {
    label: 'Logout',
    href: '/logout',
    icon: <SvgIcon name="LogoutIcon" />,
    onClick: async () => {
      await logout();
    },
  },
];

export const PageNavProfile = ({
  profile,
}: {
  profile: {
    avatarUrl: string;
    name: string;
    address: string;
  };
}) => {
  return (
    <div className="gap-space-30 flex items-center">
      <PortfolioNav />
      <DepositButton />
      <div>
        <BaseDropdownMenu>
          <BaseDropdownMenuTrigger className="border-none p-2 focus:ring-0">
            <div className="flex items-center gap-2">
              <Avatar className="h-8 w-8">
                <AvatarImage src={profile.avatarUrl} />
                <AvatarFallback>
                  <img src="/default-avatar.svg" alt="Default Avatar" className="h-full w-full" />
                </AvatarFallback>
              </Avatar>
            </div>
          </BaseDropdownMenuTrigger>
          <BaseDropdownMenuContent className="border-line min-w-[200px] p-0">
            <BaseDropdownMenuItem
              className="bg-gray-2 border-line h-(--sub-nav-height) rounded-none border-b px-[10px]"
              onSelect={event => event.preventDefault()}
            >
              <div className="flex items-center gap-2">
                <Avatar>
                  <AvatarImage src={profile.avatarUrl} />
                  <AvatarFallback>
                    <img src="/default-avatar.svg" alt="Default Avatar" className="h-full w-full" />
                  </AvatarFallback>
                </Avatar>
                <div>
                  <span className="text-size-xs text-dark">{profile.name}</span>
                  <button
                    onClick={() => {
                      copyToClipboard(profile.address);
                      toast.success('Address copied to clipboard!');
                    }}
                    className="gap-space-6 flex items-center"
                  >
                    <span className="text-size-xxs10 text-gray-3">
                      {shortenAddress(profile.address)}
                    </span>
                    <SvgIcon width={14} height={14} name="CopyIcon" />
                  </button>
                </div>
              </div>
            </BaseDropdownMenuItem>
            {LIKNS.map(link => {
              if (link.onClick) {
                return (
                  <BaseDropdownMenuItem
                    className="cursor-pointer px-[10px] py-[10px]"
                    key={link.href}
                    onClick={link.onClick}
                  >
                    <div className="flex items-center gap-2">
                      <div className="size-4">{link.icon}</div>
                      <span className="text-size-xs text-dark font-semibold">{link.label}</span>
                    </div>
                  </BaseDropdownMenuItem>
                );
              }

              return (
                <BaseDropdownMenuItem className="px-[10px] py-[10px]" key={link.href}>
                  <Link href={link.href} className="w-full">
                    <div className="flex items-center gap-2">
                      <div className="size-4">{link.icon}</div>
                      <span className="text-size-xs text-dark font-semibold">{link.label}</span>
                    </div>
                  </Link>
                </BaseDropdownMenuItem>
              );
            })}
          </BaseDropdownMenuContent>
        </BaseDropdownMenu>
      </div>
    </div>
  );
};
