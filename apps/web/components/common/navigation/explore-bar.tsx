import { SearchIcon } from 'lucide-react';
import React from 'react';
import SvgIcon from '@/components/icons/svg-icon';
import { BaseInput } from '@/components/ui/base.input';
import { CategorySwiper } from './category-swiper';

export function ExploreBar() {
  return (
    <div className="bg-gray-2 relative flex h-(--sub-nav-height) w-full flex-1 flex-col">
      <div className="flex h-full w-full">
        {/* Left - Category */}
        <CategorySwiper />

        {/* Center - Search */}
        <div className="flex-1">
          <BaseInput
            placeholder="Search by predict"
            inputClassName="text-size-xxs"
            className="bg-gray-1 h-full w-full rounded-none border-none"
            icon={<SearchIcon />}
          />
        </div>

        {/* Right - Trending */}
        <div className="px-space-20 bg-gray-2 gap-space-10 flex h-full w-[340px] items-center justify-between">
          <div className="rounded-round-sm border-line py-space-8 flex h-[36px] flex-1 items-center border">
            <div className="border-r-line border-r px-1">
              <SvgIcon name="KeywordIcon" />
            </div>
            <div className="text-dark text-size-sm px-space-10 font-semibold">
              PolWeb3 Gaminigitics
            </div>
          </div>
          <button className="border-sky rounded-round-sm flex size-[30px] items-center justify-center border bg-white">
            <SvgIcon name="AudioSettingsIcon" />
          </button>
        </div>
      </div>
    </div>
  );
}
