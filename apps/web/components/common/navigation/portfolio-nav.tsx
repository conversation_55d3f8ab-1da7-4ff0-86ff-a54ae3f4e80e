'use client';

import { useMyPortfolio } from '@/hooks/query/portfolio';
import { useMyUSDCBalance } from '@/hooks/use-usdc-balance';
import Link from 'next/link';

export default function PortfolioNav() {
  const { data: portfolio, isLoading } = useMyPortfolio();
  const { balance: usdcBalance } = useMyUSDCBalance();

  return (
    <div className="gap-space-30 flex items-center">
      <Link className="flex flex-col items-end" href="/portfolio">
        <span className="text-size-base text-yes-green font-bold">
          {isLoading ? '...' : `$${portfolio?.positionsValue}`}
        </span>
        <span className="text-size-xxs10 text-gray-500">Portfolio</span>
      </Link>
      {/* Fund */}
      <Link className="flex flex-col items-end" href="/portfolio">
        <span className="text-size-base text-no-red font-bold">${usdcBalance}</span>
        <span className="text-size-xxs10 text-gray-500">Funds</span>
      </Link>
    </div>
  );
}
