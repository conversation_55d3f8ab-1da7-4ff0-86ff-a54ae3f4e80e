import SvgIcon from '@/components/icons/svg-icon';
import * as Icons from '@/components/icons/assets';

type IconName = keyof typeof Icons;

interface PredictionMarketCardStatusProps {
  status: string;
}

// 상태에 따른 색상과 아이콘 매핑
const statusConfig: Record<string, { color: string; icon: IconName }> = {
  Live: { color: 'text-yes-green', icon: 'TimeIcon' },
  Pending: { color: 'text-gray-500', icon: 'TimeIcon' },
  Disputable: { color: 'text-orange-500', icon: 'AlertIcon' },
  'Win a dispute': { color: 'text-purple-500', icon: 'AlertIcon' },
  'Under Review': { color: 'text-blue-500', icon: 'AlertIcon' },
};

// 상태에 따른 표시 텍스트 매핑
const statusText: Record<string, string> = {
  Live: 'Live',
  Pending: 'Pending',
  Disputable: 'Disputable',
  'Win a dispute': 'Win a dispute',
  'Under Review': 'Under Review',
};

export default function PredictionMarketCardStatus({
  status = 'Live',
}: PredictionMarketCardStatusProps) {
  const config = statusConfig[status];
  const text = statusText[status];

  if (!config) {
    return null;
  }

  return (
    <div className="gap-space-6 flex items-center">
      <span className={`text-size-xxs10 ${config.color}`}>{text}</span>
      <SvgIcon data-label="icon" className={`${config.color} size-[14px]`} name={config.icon} />
    </div>
  );
}
