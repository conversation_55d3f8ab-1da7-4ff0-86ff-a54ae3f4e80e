'use client';

import { useState } from 'react';
import { Button } from '@repo/ui/components/button';
import { Card } from '@repo/ui/components/card';
import { Shield, X, Settings } from 'lucide-react';
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '@repo/ui/components/collapsible';
import { Checkbox } from '@repo/ui/components/checkbox';

interface TrackingConsentBannerProps {
  open: boolean;
  onAccept: (analytics?: boolean, clarity?: boolean) => void;
  onReject: () => void;
}

export function TrackingConsentBanner({ open, onAccept, onReject }: TrackingConsentBannerProps) {
  const [showDetails, setShowDetails] = useState(false);
  const [googleAnalytics, setGoogleAnalytics] = useState(true);
  const [microsoftClarity, setMicrosoftClarity] = useState(true);

  if (!open) return null;

  const handleAcceptAll = () => {
    setGoogleAnalytics(true);
    setMicrosoftClarity(true);
    onAccept(true, true);
  };

  const handleAcceptSelected = () => {
    onAccept(googleAnalytics, microsoftClarity);
  };

  return (
    <div className="fixed right-0 bottom-0 left-0 z-50 p-4">
      <Card className="mx-auto max-w-6xl border bg-white shadow-lg">
        <div className="p-4">
          <div className="flex items-start gap-3">
            <Shield className="mt-0.5 h-5 w-5 flex-shrink-0 text-blue-600" />

            <div className="min-w-0 flex-1">
              <div className="flex flex-col gap-3 sm:flex-row sm:items-center sm:justify-between">
                <div className="flex-1">
                  <p className="text-sm leading-relaxed text-gray-700">
                    We use <strong>Cookies</strong> to continuously improve website optimization and
                    analytics for individual users like you. For more information on how cookies are
                    used on our website,{' '}
                    <button
                      onClick={() => setShowDetails(!showDetails)}
                      className="text-blue-600 underline hover:text-blue-800"
                    >
                      see "Cookies policy"
                    </button>
                    . Click "Accept" to enable cookies or "Reject" if you do not wish to.
                  </p>
                </div>

                <div className="flex flex-shrink-0 items-center gap-2">
                  <Button
                    variant="outline"
                    onClick={onReject}
                    className="px-6 py-2 text-sm font-medium"
                  >
                    REJECT
                  </Button>
                  <Button
                    onClick={handleAcceptAll}
                    className="bg-blue-600 px-6 py-2 text-sm font-medium hover:bg-blue-700"
                  >
                    ACCEPT
                  </Button>
                </div>
              </div>

              <Collapsible open={showDetails} onOpenChange={setShowDetails}>
                <CollapsibleContent className="mt-4">
                  <div className="space-y-3 rounded-lg bg-gray-50 p-4">
                    <h4 className="mb-3 text-sm font-semibold text-gray-900">Cookie Settings</h4>

                    {/* Google Analytics */}
                    <div className="flex items-start justify-between gap-3">
                      <div className="flex-1">
                        <h5 className="text-sm font-medium text-gray-900">Google Analytics</h5>
                        <p className="mt-1 text-xs text-gray-600">
                          Analyze website usage and user behavior to improve our service.
                        </p>
                      </div>
                      <Checkbox
                        checked={googleAnalytics}
                        onCheckedChange={checked => setGoogleAnalytics(checked as boolean)}
                      />
                    </div>

                    {/* Microsoft Clarity */}
                    <div className="flex items-start justify-between gap-3">
                      <div className="flex-1">
                        <h5 className="text-sm font-medium text-gray-900">Microsoft Clarity</h5>
                        <p className="mt-1 text-xs text-gray-600">
                          Record user interactions to improve website usability.
                        </p>
                      </div>
                      <Checkbox
                        checked={microsoftClarity}
                        onCheckedChange={checked => setMicrosoftClarity(checked as boolean)}
                      />
                    </div>

                    <div className="flex justify-end pt-2">
                      <Button
                        onClick={handleAcceptSelected}
                        disabled={!googleAnalytics && !microsoftClarity}
                        className="px-4 py-2 text-sm"
                      >
                        Save Preferences
                      </Button>
                    </div>
                  </div>
                </CollapsibleContent>
              </Collapsible>
            </div>
          </div>
        </div>
      </Card>
    </div>
  );
}
