import { cn } from '@repo/ui/lib/utils';
import { GreenButton } from '../ui/base.button';
import { MarketOutcome } from '@/lib/types';
import { getGraphVar } from '@/lib/styles';

export interface PredictionOption {
  order: number;
  marketId: string;
  text: string;
  percentage: string;
  volume?: string;
}

interface PredictionContentProps {
  outcomes: MarketOutcome[];
  totalVolume: string;
  handleClick: (selection: MarketOutcome) => void;
}

export default function PredictionContent({
  outcomes,
  totalVolume,
  handleClick,
}: PredictionContentProps) {
  return (
    <div className="p-space-10">
      {/* Options list */}
      <div className="gap-space-5 flex flex-col">
        {outcomes.map(outcome => (
          <div key={outcome.outcome} className="gap-space-10 flex items-center">
            {/* Option Text */}
            <div className="text-gray-3 text-size-xs w-[90px] truncate font-semibold">
              {outcome.outcome}
            </div>

            {/* Progress */}
            <div className="gap-space-10 flex flex-1 flex-row items-center justify-between">
              <div data-name="prediction-progress" className="relative h-2 w-full overflow-hidden">
                <div
                  data-name="prediction-progress-bar"
                  className={cn('absolute top-0 left-0 h-full rounded-none')}
                  style={{
                    backgroundColor: getGraphVar(outcome.order),
                    width: (Number(outcome.volume) / Number(totalVolume)) * 100 + '%',
                  }} // TODO: remove default value
                />
              </div>
              <div>
                <span className="text-size-xs text-mid-dark font-semibold whitespace-nowrap">
                  {Math.floor((Number(outcome.volume) / Number(totalVolume)) * 100)}%
                </span>
              </div>
            </div>
            {/* Button  */}
            <GreenButton
              rounded="sm"
              fontSize="xxs"
              onClick={() => handleClick(outcome)}
              className="px-space-6"
              size="xs"
            >
              Predict
            </GreenButton>
          </div>
        ))}
      </div>
    </div>
  );
}
