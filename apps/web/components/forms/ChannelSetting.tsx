'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@repo/ui/components/form';
import { BaseInput } from '@/components/ui/base.input';
import { BaseTextarea } from '@/components/ui/base.textarea';
import { BaseButton, DarkButton } from '@/components/ui/base.button';
import { pxToRem } from '@repo/ui/lib/utils';
import SvgIcon from '@/components/icons/svg-icon';
import { UpdateChannelRequestBody } from '@/lib/api/channel/channel.schema';
import { toast } from '@/components/ui/base.toast';
import { DEFAULT_MARKET_AVATAR_URL } from '@/lib/constants';
import { useUpdateChannel } from '@/hooks/query/channel';

// SNS 타입 정의
const SNS_TYPES = [
  'youtube',
  'twitter',
  'telegram',
  'facebook',
  'discord',
  'tiktok',
  'instagram',
  'abstract',
] as const;

// Form schema based on UpdateChannelReqDto
const channelFormSchema = z.object({
  name: z
    .string()
    .max(20, 'Channel name must be at most 20 characters.')
    .regex(/^[a-zA-Z0-9\s]+$/, 'Channel name must contain only letters, numbers, and spaces.')
    .optional(),
  description: z.string().max(255, 'Description must be at most 255 characters.').optional(),
  channelSns: z
    .array(
      z.object({
        snsType: z.enum(SNS_TYPES),
        snsUrl: z.union([z.string(), z.literal('')]),
      })
    )
    .optional(),
});

type ChannelFormValues = z.infer<typeof channelFormSchema>;

const PLACEHOLDER_TEXT = {
  channelName: 'Enter channel name (a-z, A-Z, 0-9, max 20 chars)',
  description:
    'Please write about your channel. Describe what your channel will focus on, your content, and what users can expect.',
};

const SOCIAL_MEDIA = {
  youtube: {
    key: 'youtube',
    icon: <SvgIcon name="YoutubeIcon" />,
    placeholder: 'Enter your YouTube channel URL',
  },
  twitter: {
    key: 'twitter',
    icon: <SvgIcon name="XIcon" />,
    placeholder: 'Enter your X (Twitter) URL',
  },
  facebook: {
    key: 'facebook',
    icon: <SvgIcon name="FacebookIcon" />,
    placeholder: 'Enter your Facebook page URL',
  },
  telegram: {
    key: 'telegram',
    icon: <SvgIcon name="TelegramIcon" />,
    placeholder: 'Enter your Telegram URL',
  },
  discord: {
    key: 'discord',
    icon: <SvgIcon name="DiscordIcon" />,
    placeholder: 'Enter your Discord URL',
  },
  tiktok: {
    key: 'tiktok',
    icon: <SvgIcon name="TiktokIcon" />,
    placeholder: 'Enter your TikTok URL',
  },
  instagram: {
    key: 'instagram',
    icon: <SvgIcon name="InstagramIcon" />,
    placeholder: 'Enter your Instagram URL',
  },
  abstract: {
    key: 'abstract',
    icon: <SvgIcon name="AbstractIcon" />,
    placeholder: 'Enter your Abstract URL',
  },
};

interface ChannelSettingProps {
  initialData?: {
    name?: string;
    description?: string;
    imageUrl?: string;
    bannerUrl?: string;
    channelSns?: Array<{
      snsType: string;
      snsUrl: string;
    }>;
  };
}

export default function ChannelSetting({ initialData }: ChannelSettingProps) {
  const [bannerFile, setBannerFile] = useState<File | null>(null);
  const [bannerPreview, setBannerPreview] = useState<string>(initialData?.bannerUrl || '');
  const [avatarFile, setAvatarFile] = useState<File | null>(null);
  const [avatarPreview, setAvatarPreview] = useState<string>(
    initialData?.imageUrl || DEFAULT_MARKET_AVATAR_URL
  );

  const updateChannelMutation = useUpdateChannel();

  // Initialize SNS URLs
  const initialChannelSns = SNS_TYPES.map(snsType => {
    const existingSns = initialData?.channelSns?.find(sns => sns.snsType === snsType);
    return {
      snsType,
      snsUrl: existingSns?.snsUrl || '',
    };
  });

  const form = useForm<ChannelFormValues>({
    resolver: zodResolver(channelFormSchema),
    defaultValues: {
      name: initialData?.name || '',
      description: initialData?.description || '',
      channelSns: initialChannelSns,
    },
  });

  // Watch form values for changes
  const watchedValues = form.watch();

  // Function to check if there are any changes
  const hasChanges = () => {
    // Check if name has changed
    if (watchedValues.name && watchedValues.name !== initialData?.name) {
      return true;
    }

    // Check if description has changed
    if (
      watchedValues.description !== undefined &&
      watchedValues.description !== initialData?.description
    ) {
      return true;
    }

    // Check if avatar file is uploaded
    if (avatarFile) {
      return true;
    }

    // Check if banner file is uploaded
    if (bannerFile) {
      return true;
    }

    // Check if any SNS URLs have changed
    if (watchedValues.channelSns) {
      const currentChannelSns = watchedValues.channelSns
        .filter(sns => sns.snsUrl.trim() !== '')
        .map(sns => ({
          snsType: sns.snsType,
          snsUrl: sns.snsUrl,
        }));

      const initialChannelSnsSorted = (initialData?.channelSns || [])
        .filter(sns => sns.snsUrl.trim() !== '')
        .map(sns => ({
          snsType: sns.snsType,
          snsUrl: sns.snsUrl,
        }))
        .sort((a, b) => a.snsType.localeCompare(b.snsType));

      const currentChannelSnsSorted = currentChannelSns.sort((a, b) =>
        a.snsType.localeCompare(b.snsType)
      );

      if (JSON.stringify(initialChannelSnsSorted) !== JSON.stringify(currentChannelSnsSorted)) {
        return true;
      }
    }

    return false;
  };

  const handleBannerUpload = () => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = 'image/jpeg,image/png';
    input.onchange = e => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (file) {
        handleBannerFileSelect(file);
      }
    };
    input.click();
  };

  const handleBannerFileSelect = (file: File) => {
    if (!file.type.startsWith('image/')) {
      console.error('Please select an image file');
      return;
    }

    if (file.size > 5 * 1024 * 1024) {
      console.error('File size must be less than 5MB');
      return;
    }

    setBannerFile(file);

    const reader = new FileReader();
    reader.onload = e => {
      const result = e.target?.result as string;
      setBannerPreview(result);
    };
    reader.readAsDataURL(file);
  };

  const handleAvatarUpload = () => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = 'image/jpeg,image/png';
    input.onchange = e => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (file) {
        handleAvatarFileSelect(file);
      }
    };
    input.click();
  };

  const handleAvatarFileSelect = (file: File) => {
    if (!file.type.startsWith('image/')) {
      console.error('Please select an image file');
      return;
    }

    if (file.size > 5 * 1024 * 1024) {
      console.error('File size must be less than 5MB');
      return;
    }

    setAvatarFile(file);

    const reader = new FileReader();
    reader.onload = e => {
      const result = e.target?.result as string;
      setAvatarPreview(result);
    };
    reader.readAsDataURL(file);
  };

  const onSubmit = async (values: ChannelFormValues) => {
    try {
      const updateData: UpdateChannelRequestBody = {};

      // Only include fields that have been changed
      if (values.name && values.name !== initialData?.name) {
        updateData.name = values.name;
      }

      if (values.description !== undefined && values.description !== initialData?.description) {
        updateData.description = values.description;
      }

      if (avatarFile) {
        updateData.image = avatarFile;
      }

      if (bannerFile) {
        updateData.banner = bannerFile;
      }

      // Filter out empty SNS URLs and only include changed ones
      if (values.channelSns) {
        const filteredChannelSns = values.channelSns
          .filter(sns => sns.snsUrl.trim() !== '')
          .map(sns => ({
            snsType: sns.snsType as any,
            snsUrl: sns.snsUrl,
          }));

        if (filteredChannelSns.length > 0) {
          updateData.channelSns = filteredChannelSns;
        }
      }

      // Only call API if there are changes
      if (Object.keys(updateData).length > 0) {
        await updateChannelMutation.mutateAsync(updateData);
        toast.success('Channel updated successfully!');
      } else {
        console.log('No changes to save');
      }
    } catch (error) {
      console.error('Failed to update channel:', error);
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="gap-space-30 flex flex-col">
        {/* Error Display */}
        {updateChannelMutation.error && (
          <div className="rounded-md border border-red-200 bg-red-50 p-4">
            <div className="flex">
              <div className="ml-3">
                <h3 className="text-sm font-medium text-red-800">Channel update failed</h3>
                <div className="mt-2 text-sm text-red-700">
                  {updateChannelMutation.error.message}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Banner Upload Section */}
        <section className="gap-space-20 flex flex-col">
          <p className="text-size-sm text-gray-3">
            Set a representative image for your channel. Applying it service-wide may take up to
            five minutes.
          </p>
          <div
            data-role="upload-banner-image"
            className="bg-gray-2 border-line relative h-(--banner-image-height) w-full overflow-hidden rounded-lg border"
          >
            <div
              className="h-full w-full"
              style={{
                // backgroundSize: 'cover',
                backgroundRepeat: 'no-repeat',
                backgroundPosition: 'center',
                backgroundImage: `url('${bannerPreview}')`,
              }}
            ></div>
            <div className="gap-space-20 absolute top-1/2 left-1/2 flex -translate-x-1/2 -translate-y-1/2 transform flex-col items-center">
              <BaseButton
                type="button"
                variant="info"
                aria-label="Upload banner image"
                onClick={handleBannerUpload}
              >
                <SvgIcon name="ImageFillIcon" />
                Upload new photo
              </BaseButton>
              <p className="text-size-xs text-gray-3 text-center">
                At least 1258px X 237px recommended.
                <br /> JPG or PNG is allowed.
              </p>
            </div>
          </div>
        </section>

        {/* Avatar Upload Section */}
        <section>
          <div data-role="upload-icon-image" className="gap-space-30 flex items-center">
            <button
              type="button"
              data-role="upload-image"
              className="relative size-[80px]"
              onClick={handleAvatarUpload}
            >
              <div
                className="bg-gray-2 border-line h-full w-full rounded-full border"
                style={{
                  backgroundSize: 'cover',
                  backgroundPosition: 'center',
                  backgroundImage: `url('${avatarPreview}')`,
                }}
              ></div>
              <div
                style={{
                  right: '0px',
                  bottom: '0px',
                }}
                className="bg-sky absolute flex size-[24px] items-center justify-center rounded-full"
              >
                <svg
                  width="10"
                  height="10"
                  viewBox="0 0 10 10"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M3.86229 9.5V0.5H6.13771V9.5H3.86229ZM0.5 6.13771V3.86229H9.5V6.13771H0.5Z"
                    fill="white"
                  />
                </svg>
              </div>
            </button>
            <div className="text-sm text-gray-500">
              <div>At least 80px X 80px recommended. JPG or PNG is allowed.</div>
            </div>
          </div>
        </section>

        {/* SNS Links Section */}
        <section>
          <div className="gap-space-10 flex flex-col">
            {SNS_TYPES.map((snsType, index) => {
              const snsInfo = SOCIAL_MEDIA[snsType];
              return (
                <FormField
                  key={snsType}
                  control={form.control}
                  name={`channelSns.${index}.snsUrl`}
                  render={({ field }) => (
                    <FormItem>
                      <div className="gap-space-15 flex items-center">
                        <div>{snsInfo.icon}</div>
                        <FormControl>
                          <BaseInput placeholder={snsInfo.placeholder} {...field} />
                        </FormControl>
                      </div>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              );
            })}
          </div>
        </section>

        {/* Channel Info Section */}
        <section className="gap-space-30 flex flex-col">
          <header className="text-size-base text-mid-dark font-bold">Channel Info</header>
          <div className="gap-space-30 flex flex-col md:flex-row">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem data-role="write-channel-name" className="space-y-space-15 flex-1">
                  <FormLabel className="text-size-sm text-mid-dark block font-semibold">
                    Channel Name
                  </FormLabel>
                  <FormControl>
                    <BaseInput placeholder={PLACEHOLDER_TEXT.channelName} {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <FormField
            control={form.control}
            name="description"
            render={({ field }) => (
              <FormItem data-role="write-channel-description" className="space-y-space-15">
                <FormLabel className="text-size-sm text-mid-dark block font-semibold">
                  About Channel
                </FormLabel>
                <FormControl>
                  <BaseTextarea
                    style={{ height: pxToRem(224) }}
                    placeholder={PLACEHOLDER_TEXT.description}
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </section>

        <DarkButton
          type="submit"
          size="lg"
          fontSize="base"
          disabled={updateChannelMutation.isPending || !hasChanges()}
          style={{
            width: pxToRem(148),
          }}
        >
          {updateChannelMutation.isPending ? 'Saving...' : 'Save changes'}
        </DarkButton>
      </form>
    </Form>
  );
}
