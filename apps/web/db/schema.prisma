// ===================================================================
// Prisma Schema for Dance APIs
// ===================================================================
// This schema defines the data structure for the Dance platform
// All relations are handled via transactions instead of references

generator client {
  provider      = "prisma-client-js"
  binaryTargets = ["native", "rhel-openssl-3.0.x", "linux-arm64-openssl-3.0.x"]
}

generator drizzle {
  provider = "drizzle-prisma-generator"
  output   = "./drizzle" // Output directory for generated Drizzle schema
}

// generator zod {
//   provider = "zod-prisma-types"
//   output   = "../libs/interfaces/src/prisma-zod"
// }

datasource db {
  provider = "mysql"
  // url      = env("PRISMA_DATABASE_URL") // Set via environment variables for security
  url      =  env("DATABASE_URL")
}

// ===================================================================
// Blockchain Event Models
// ===================================================================

/// Raw blockchain event log data captured from the network
/// Stores the original log format before any parsing/processing
model EventRaw {
  id               Int      @id @default(autoincrement())
  contractAddress  String // The contract that emitted the event
  transactionHash  String // Transaction hash containing this event
  transactionIndex Int // Index position of the transaction in the block
  logIndex         Int // Index position of the log in the transaction
  blockHash        String // Hash of the block containing this event
  blockNumber      Int // Block number containing this event
  blockTimestamp   DateTime // Timestamp when the block was mined
  data             String   @db.Text // Raw event data
  topics           String   @db.Text // Event topics (JSON string array)

  @@unique([transactionHash, logIndex], name: "transactionHash_logIndex")
  @@unique([blockNumber, logIndex], name: "blockNumber_logIndex")
  @@index([blockNumber, logIndex], name: "blockNumber_logIndex")
  @@map("event_raw")
}

/// Parsed blockchain events with structured data
/// Related to EventRaw by blockNumber and logIndex
model Event {
  eventName   String // Name of the event (e.g., "Bet", "Settled")
  eventArgs   String @db.Text // JSON string of parsed event arguments
  blockNumber Int // Block number containing this event (join with EventRaw)
  logIndex    Int // Log index in the block (join with EventRaw)

  @@id([blockNumber, logIndex], name: "blockNumber_logIndex")
  @@map("event")
}

// ===================================================================
// Market Models
// ===================================================================

/// Prediction market with multiple possible outcomes
/// Central entity for betting activities
model Market {
  id                    String    @id // Unique market identifier (bytes32)
  channelId             String // Channel that created this market (join with Channel)
  title                 String // Market title/question
  description           String    @db.Text // Detailed market description
  imageUrl              String? // Optional image URL for the market
  maker                 String // Address of the market creator
  categoryName          String // Category of the market (join with Category)
  predictionDeadline    DateTime // Deadline for placing bets
  resultConfirmDeadline DateTime // Deadline for confirming results
  disputedPeriod        String // Duration of the dispute period
  collateralAmount      BigInt // Amount of tokens required to create the market
  broadcastURL          String? // Optional URL for broadcasting the market
  referenceURL          String // URL for reference
  outcomeProposedAt     DateTime? // When the outcome was proposed
  disputedAt            DateTime? // When the market was disputed
  finalizedAt           DateTime? // When the market was finalized
  winningOutcome        String? // Outcome that won the market
  disputedOutcome       String? // Outcome that was disputed
  totalVolume           BigInt    @default(0) // Total volume of bets placed on the market
  createdAt             DateTime  @default(now()) // When the market was created

  @@index([channelId], name: "channelId")
  @@index([categoryName], name: "categoryName")
  @@map("market")
}

/// User position (bet) on a specific market outcome
/// Tracks user's betting activity and status
model Position {
  userAddress String // User who placed the bet (join with User)
  marketId    String // Market of the bet (join with Market)
  outcome     String // Outcome the user bet on (join with Outcome)
  value       BigInt // Amount of tokens bet
  refundedAt  DateTime? // When the bet was refunded (if applicable)
  redeemedAt  DateTime? // When winnings were redeemed (if applicable)
  createdAt   DateTime  @default(now()) // When the position was created
  updatedAt   DateTime  @updatedAt // When the position was last updated

  @@unique([userAddress, marketId, outcome], name: "userAddress_marketId_outcome")
  @@index([userAddress, redeemedAt, refundedAt, updatedAt], name: "userAddress_status_updatedAt") // 사용자의 활성 포지션 내림차순 조회 (getPositionsOrderByUpdatedAt)
  @@index([userAddress, redeemedAt, refundedAt], name: "userAddress_status") // 사용자의 활성 포지션 집계 (getPositionsValue)
  @@index([marketId], name: "marketId")
  @@index([marketId, outcome], name: "marketId_outcome")
  @@map("position")
}

// ===================================================================
// Categorization Models
// ===================================================================

/// Top-level category for markets
/// Used for organizing markets into broad topics
model Category {
  name  String @id // Category name (e.g., "Sports", "Politics")
  order Int    @unique

  @@map("category")
}

// ===================================================================
// User Models
// ===================================================================

/// User profile and statistics
/// Represents a user in the platform
model User {
  address      String   @id // User's EOA Address
  proxyAddress String   @unique // User's Safe Address
  nickname     String?  @unique // User's display name (automatically indexed)
  referralCode String   @unique // Unique referral code for the user
  referrer     String? // Referrer address (join with User)
  bio          String   @default("") @db.Text // User's biography/description
  email        String?  @unique // User's email address
  imageUrl     String?  @db.Text // User's profile image URL
  createdAt    DateTime @default(now()) // When the user profile was created
  updatedAt    DateTime @updatedAt // When the user profile was last updated

  @@index([proxyAddress], name: "proxyAddress")
  @@map("user")
}

/// User activity record
/// Tracks all user actions on the platform for history and analytics
model Activity {
  id               Int      @id @default(autoincrement()) // Unique activity identifier
  marketId         String? // Market involved in the activity (join with Market)
  type             String // Activity type (e.g., "Bet", "Redeem", "Claim")
  outcome          String? // Outcome involved in the activity
  value            BigInt? // Token amount involved in the activity
  distributionType String? // Type of distribution (e.g., "REVENUE", "COLLATERAL")
  userAddress      String // User who performed the activity (join with User)
  timestamp        DateTime // When the activity occurred
  blockNumber      Int // Block number containing this event
  logIndex         Int // Log index in the block

  @@index([userAddress, type], name: "userAddress_type") // 사용자+이벤트 타입 조회 (getBetCount)
  @@index([userAddress, blockNumber, logIndex], name: "userAddress_blockNumber_logIndex") // 사용자 활동 조회 (getUserActivities)
  @@index([marketId], name: "marketId")
  @@index([blockNumber, logIndex], name: "blockNumber_logIndex")
  @@map("activity")
}

// ===================================================================
// Channel Models
// ===================================================================

/// Content creator channel
/// Platform for creators to publish prediction markets
model Channel {
  id          String   @id // Address of the channel leader (join with User)
  name        String   @unique // Display name of the channel
  imageUrl    String? // Channel profile image URL
  description String?  @db.Text // Channel description
  createdAt   DateTime // When the channel was created
  updatedAt   DateTime @updatedAt // When the channel was last updated

  // Removed redundant index as the primary key (id) already serves as an index
  @@map("channel")
}

/// Channel management permissions
/// Allows channel leaders to delegate management rights
model ChannelManager {
  channelId      String // Channel being managed (join with Channel)
  managerAddress String // User with management rights (join with User)
  createdAt      DateTime @default(now()) // When the manager was added

  @@id([channelId, managerAddress])
  @@index([channelId], name: "channelId")
  @@index([managerAddress], name: "managerAddress")
  @@map("channel_manager")
}

/// User subscription to a channel
/// Tracks channel follower relationships
model Subscription {
  userAddress String // Subscriber user address (join with User)
  channelId   String // Channel address being subscribed to (join with Channel)
  createdAt   DateTime @default(now()) // When the subscription was created

  @@id([userAddress, channelId]) // Primary key serves as an index
  // Removed individual indexes as the composite primary key already covers these cases
  @@map("subscription")
}

// ===================================================================
// Distribution Models
// ===================================================================

model Distribution {
  id               Int      @id @default(autoincrement()) // Unique distribution identifier
  userAddress      String // User receiving the distribution (join with User)
  marketId         String // Market associated with the distribution (join with Market)
  distributionType String // Type of distribution (e.g., "REVENUE", "COLLATERAL")
  category         String // Category of the distribution (e.g., "Referral", "PlatformFee")
  value            BigInt // Amount of tokens distributed
  timestamp        DateTime // When the activity occurred
  blockNumber      Int // Block number containing this event
  logIndex         Int // Log index in the block

  @@map("distribution")
}

// ===================================================================
// Dispute Models
// ===================================================================

model Dispute {
  id           Int      @id @default(autoincrement()) // Unique dispute identifier
  marketId     String // Market being disputed (join with Market)
  userAddress  String // User who initiated the dispute (join with User)
  amount       BigInt // Amount of tokens involved in the dispute
  description  String?  @db.Text // Description of the dispute
  referenceURL String? // Optional URL for reference
  timestamp    DateTime // When the activity occurred
  blockNumber  Int // Block number containing this event
  logIndex     Int // Log index in the block

  @@index([marketId, blockNumber, logIndex], name: "marketId_blockNumber_logIndex") // Index for market disputes
  @@index([userAddress, blockNumber, logIndex], name: "userAddress_blockNumber_logIndex") // Index for user disputes
  @@map("dispute")
}

// ===================================================================
// Referral Models
// ===================================================================

model ReferrerProfit {
  userAddress String // User receiving the referral profit (join with User)
  profit      BigInt // Amount of tokens received as profit
  month       String // Month of the profit (YYYYMM format)
  claimedAt   DateTime? // When the profit was claimed

  @@id([userAddress, month]) // Composite primary key
  @@index([userAddress, claimedAt], name: "userAddress_claimedAt") // Index for user and claimedAt
  @@index([month], name: "month") // Index for month
  @@map("referral_profit")
}

model ReferralBenefit {
  level                 Int    @id
  commissionRewardRatio Float // Commission reward for the referrer
  feeRebateRatio        Float // Fee rebate for the referrer
  accumulatedProfit     BigInt // Accumulated profit for the referrer

  @@map("referral_benefit")
}

// ===================================================================
// Mail verification Models
// ===================================================================

model MailVerification {
  userAddress    String @id // User's proxy address (join with User)
  email          String @unique // User's email address
  verificationId String @unique

  @@unique([userAddress, email], name: "userAddress_email") // Unique constraint for user and email
  @@map("mail_verification")
}
