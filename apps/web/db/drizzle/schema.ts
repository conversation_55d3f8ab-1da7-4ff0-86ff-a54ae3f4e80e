import {
  bigint,
  datetime,
  double,
  int,
  mysqlTable,
  primaryKey,
  uniqueIndex,
  varchar,
} from 'drizzle-orm/mysql-core';
import { sql } from 'drizzle-orm';

export const EventRaw = mysqlTable(
  'event_raw',
  {
    id: int('id').notNull().primaryKey().autoincrement(),
    contractAddress: varchar('contractAddress', { length: 191 }).notNull(),
    transactionHash: varchar('transactionHash', { length: 191 }).notNull(),
    transactionIndex: int('transactionIndex').notNull(),
    logIndex: int('logIndex').notNull(),
    blockHash: varchar('blockHash', { length: 191 }).notNull(),
    blockNumber: int('blockNumber').notNull(),
    blockTimestamp: datetime('blockTimestamp', { fsp: 3 }).notNull(),
    data: varchar('data', { length: 191 }).notNull(),
    topics: varchar('topics', { length: 191 }).notNull(),
  },
  EventRaw => [
    uniqueIndex('transactionHash_logIndex').on(EventRaw.transactionHash, EventRaw.logIndex),
    uniqueIndex('blockNumber_logIndex').on(EventRaw.blockNumber, EventRaw.logIndex),
  ]
);

export const Event = mysqlTable(
  'event',
  {
    eventName: varchar('eventName', { length: 191 }).notNull(),
    eventArgs: varchar('eventArgs', { length: 191 }).notNull(),
    blockNumber: int('blockNumber').notNull(),
    logIndex: int('logIndex').notNull(),
  },
  Event => [
    primaryKey({
      name: 'blockNumber_logIndex',
      columns: [Event.blockNumber, Event.logIndex],
    }),
  ]
);

export const Market = mysqlTable('market', {
  id: varchar('id', { length: 191 }).notNull().primaryKey(),
  channelId: varchar('channelId', { length: 191 }).notNull(),
  title: varchar('title', { length: 191 }).notNull(),
  description: varchar('description', { length: 191 }).notNull(),
  imageUrl: varchar('imageUrl', { length: 191 }),
  maker: varchar('maker', { length: 191 }).notNull(),
  categoryName: varchar('categoryName', { length: 191 }).notNull(),
  predictionDeadline: datetime('predictionDeadline', { fsp: 3 }).notNull(),
  resultConfirmDeadline: datetime('resultConfirmDeadline', {
    fsp: 3,
  }).notNull(),
  disputedPeriod: varchar('disputedPeriod', { length: 191 }).notNull(),
  collateralAmount: bigint('collateralAmount', { mode: 'bigint' }).notNull(),
  broadcastURL: varchar('broadcastURL', { length: 191 }),
  referenceURL: varchar('referenceURL', { length: 191 }).notNull(),
  outcomeProposedAt: datetime('outcomeProposedAt', { fsp: 3 }),
  disputedAt: datetime('disputedAt', { fsp: 3 }),
  finalizedAt: datetime('finalizedAt', { fsp: 3 }),
  winningOutcome: varchar('winningOutcome', { length: 191 }),
  disputedOutcome: varchar('disputedOutcome', { length: 191 }),
  totalVolume: bigint('totalVolume', { mode: 'bigint' }).notNull().default(0n),
  createdAt: datetime('createdAt', { fsp: 3 })
    .notNull()
    .default(sql`CURRENT_TIMESTAMP`),
});

export const Position = mysqlTable(
  'position',
  {
    userAddress: varchar('userAddress', { length: 191 }).notNull(),
    marketId: varchar('marketId', { length: 191 }).notNull(),
    outcome: varchar('outcome', { length: 191 }).notNull(),
    value: bigint('value', { mode: 'bigint' }).notNull(),
    refundedAt: datetime('refundedAt', { fsp: 3 }),
    redeemedAt: datetime('redeemedAt', { fsp: 3 }),
    createdAt: datetime('createdAt', { fsp: 3 })
      .notNull()
      .default(sql`CURRENT_TIMESTAMP`),
    updatedAt: datetime('updatedAt', { fsp: 3 }).notNull(),
  },
  Position => [
    uniqueIndex('userAddress_marketId_outcome').on(
      Position.userAddress,
      Position.marketId,
      Position.outcome
    ),
  ]
);

export const Category = mysqlTable('category', {
  name: varchar('name', { length: 191 }).notNull().primaryKey(),
  order: int('order').notNull().unique(),
});

export const User = mysqlTable('user', {
  address: varchar('address', { length: 191 }).notNull().primaryKey(),
  proxyAddress: varchar('proxyAddress', { length: 191 }).notNull().unique(),
  nickname: varchar('nickname', { length: 191 }).unique(),
  referralCode: varchar('referralCode', { length: 191 }).notNull().unique(),
  referrer: varchar('referrer', { length: 191 }),
  bio: varchar('bio', { length: 191 }).notNull(),
  email: varchar('email', { length: 191 }).unique(),
  imageUrl: varchar('imageUrl', { length: 191 }),
  createdAt: datetime('createdAt', { fsp: 3 })
    .notNull()
    .default(sql`CURRENT_TIMESTAMP`),
  updatedAt: datetime('updatedAt', { fsp: 3 }).notNull(),
});

export const Activity = mysqlTable('activity', {
  id: int('id').notNull().primaryKey().autoincrement(),
  marketId: varchar('marketId', { length: 191 }),
  type: varchar('type', { length: 191 }).notNull(),
  outcome: varchar('outcome', { length: 191 }),
  value: bigint('value', { mode: 'bigint' }),
  distributionType: varchar('distributionType', { length: 191 }),
  userAddress: varchar('userAddress', { length: 191 }).notNull(),
  timestamp: datetime('timestamp', { fsp: 3 }).notNull(),
  blockNumber: int('blockNumber').notNull(),
  logIndex: int('logIndex').notNull(),
});

export const Channel = mysqlTable('channel', {
  id: varchar('id', { length: 191 }).notNull().primaryKey(),
  name: varchar('name', { length: 191 }).notNull().unique(),
  imageUrl: varchar('imageUrl', { length: 191 }),
  description: varchar('description', { length: 191 }),
  createdAt: datetime('createdAt', { fsp: 3 }).notNull(),
  updatedAt: datetime('updatedAt', { fsp: 3 }).notNull(),
});

export const ChannelManager = mysqlTable(
  'channel_manager',
  {
    channelId: varchar('channelId', { length: 191 }).notNull(),
    managerAddress: varchar('managerAddress', { length: 191 }).notNull(),
    createdAt: datetime('createdAt', { fsp: 3 })
      .notNull()
      .default(sql`CURRENT_TIMESTAMP`),
  },
  ChannelManager => [
    primaryKey({
      name: 'ChannelManager_cpk',
      columns: [ChannelManager.channelId, ChannelManager.managerAddress],
    }),
  ]
);

export const Subscription = mysqlTable(
  'subscription',
  {
    userAddress: varchar('userAddress', { length: 191 }).notNull(),
    channelId: varchar('channelId', { length: 191 }).notNull(),
    createdAt: datetime('createdAt', { fsp: 3 })
      .notNull()
      .default(sql`CURRENT_TIMESTAMP`),
  },
  Subscription => [
    primaryKey({
      name: 'Subscription_cpk',
      columns: [Subscription.userAddress, Subscription.channelId],
    }),
  ]
);

export const Distribution = mysqlTable('distribution', {
  id: int('id').notNull().primaryKey().autoincrement(),
  userAddress: varchar('userAddress', { length: 191 }).notNull(),
  marketId: varchar('marketId', { length: 191 }).notNull(),
  distributionType: varchar('distributionType', { length: 191 }).notNull(),
  category: varchar('category', { length: 191 }).notNull(),
  value: bigint('value', { mode: 'bigint' }).notNull(),
  timestamp: datetime('timestamp', { fsp: 3 }).notNull(),
  blockNumber: int('blockNumber').notNull(),
  logIndex: int('logIndex').notNull(),
});

export const Dispute = mysqlTable('dispute', {
  id: int('id').notNull().primaryKey().autoincrement(),
  marketId: varchar('marketId', { length: 191 }).notNull(),
  userAddress: varchar('userAddress', { length: 191 }).notNull(),
  amount: bigint('amount', { mode: 'bigint' }).notNull(),
  description: varchar('description', { length: 191 }),
  referenceURL: varchar('referenceURL', { length: 191 }),
  timestamp: datetime('timestamp', { fsp: 3 }).notNull(),
  blockNumber: int('blockNumber').notNull(),
  logIndex: int('logIndex').notNull(),
});

export const ReferrerProfit = mysqlTable(
  'referral_profit',
  {
    userAddress: varchar('userAddress', { length: 191 }).notNull(),
    profit: bigint('profit', { mode: 'bigint' }).notNull(),
    month: varchar('month', { length: 191 }).notNull(),
    claimedAt: datetime('claimedAt', { fsp: 3 }),
  },
  ReferrerProfit => [
    primaryKey({
      name: 'ReferrerProfit_cpk',
      columns: [ReferrerProfit.userAddress, ReferrerProfit.month],
    }),
  ]
);

export const ReferralBenefit = mysqlTable('referral_benefit', {
  level: int('level').notNull().primaryKey(),
  commissionRewardRatio: double('commissionRewardRatio').notNull(),
  feeRebateRatio: double('feeRebateRatio').notNull(),
  accumulatedProfit: bigint('accumulatedProfit', { mode: 'bigint' }).notNull(),
});

export const MailVerification = mysqlTable(
  'mail_verification',
  {
    userAddress: varchar('userAddress', { length: 191 }).notNull().primaryKey(),
    email: varchar('email', { length: 191 }).notNull().unique(),
    verificationId: varchar('verificationId', { length: 191 }).notNull().unique(),
  },
  MailVerification => [
    uniqueIndex('userAddress_email').on(MailVerification.userAddress, MailVerification.email),
  ]
);
